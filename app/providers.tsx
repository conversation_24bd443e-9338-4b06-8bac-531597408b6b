"use client";

import { ThemeProvider as NextThemesProvider } from "next-themes";
import { NotificationProvider } from "@/contexts/notification-context";
import { AbsenceProvider } from "@/contexts/absence-context";
import { ReactNode, useEffect } from "react";
import { loadMockDataFromStorage } from "@/lib/load-mock-data";
import { SyncDriverData } from "@/components/sync-driver-data";
import { InitializeMockData } from "@/components/initialize-mock-data";
import {
  initializeMockData,
  updateMockDataFromLocalStorage,
} from "@/lib/initialize-mock-data";

export function Providers({ children }: { children: ReactNode }) {
  // Carregar dados mockados do localStorage quando a aplicação iniciar
  useEffect(() => {
    // Inicializar os dados mockados com os dados padrão se não existirem no localStorage
    initializeMockData();

    // Carregar os dados do perfil do driver do localStorage
    loadMockDataFromStorage();

    // Atualizar os dados mockados com os dados do localStorage
    updateMockDataFromLocalStorage();

    // Adicionar um listener para atualizar os dados quando o localStorage for alterado
    const handleStorageChange = () => {
      console.log("Storage changed, updating mock data...");
      updateMockDataFromLocalStorage();
    };

    window.addEventListener("storage", handleStorageChange);

    // Limpar o listener quando o componente for desmontado
    return () => {
      window.removeEventListener("storage", handleStorageChange);
    };
  }, []);

  return (
    <NextThemesProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      <NotificationProvider>
        <AbsenceProvider>
          {children}
          <SyncDriverData />
          <InitializeMockData />
        </AbsenceProvider>
      </NotificationProvider>
    </NextThemesProvider>
  );
}
