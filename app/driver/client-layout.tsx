"use client";

import { useState, useEffect } from "react";
import { DashboardNav } from "@/components/dashboard-nav";
import { ThemeToggle } from "@/components/theme-toggle";
import { AdminUserNav } from "@/components/admin-user-nav";
import { Menu, X, Truck, LogOut, User } from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";

interface ClientDriverLayoutProps {
  children: React.ReactNode;
  session: {
    user: {
      name?: string;
      email?: string;
      role?: string;
    };
  };
}

// Definer navigasjonselementer spesifikke for sjåføren
const driverNavItems = [
  {
    title: "Dashboard",
    href: "/driver/dashboard",
    icon: Truck,
  },
  {
    title: "Mine oppdrag",
    href: "/driver/bookings",
    icon: Truck,
  },
  {
    title: "Timeplan",
    href: "/driver/schedule",
    icon: Truck,
  },
  {
    title: "Profil",
    href: "/driver/profile",
    icon: Truck,
  },
];

// Definer grensesnittet for DriverNav-props
interface DriverNavProps {
  onLinkClick: () => void;
}

// Navigasjonskomponent spesifikk for sjåføren
function DriverNav({ onLinkClick }: DriverNavProps) {
  return (
    <div className="space-y-4">
      <div className="px-3 py-2">
        <h2 className="mb-2 px-2 text-xs font-semibold tracking-tight">
          Sjåfør Meny
        </h2>
        <nav className="grid items-start gap-1.5">
          {driverNavItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className="group flex items-center rounded-md px-3 py-2.5 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground"
              onClick={onLinkClick}
            >
              <item.icon className="mr-3 h-5 w-5" />
              <span>{item.title}</span>
            </Link>
          ))}
          <Link
            href="/auth/logout"
            className="group flex items-center rounded-md px-3 py-2.5 text-sm font-medium text-red-500 transition-colors hover:bg-red-100 hover:text-red-600 dark:hover:bg-red-900/20"
            onClick={onLinkClick}
          >
            <LogOut className="mr-3 h-5 w-5" />
            <span>Logg ut</span>
          </Link>
        </nav>
      </div>
    </div>
  );
}

export default function ClientDriverLayout({
  children,
  session,
}: ClientDriverLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [userName, setUserName] = useState("");
  
  // Efeito para inicializar o nome do usuário a partir do localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedName = localStorage.getItem('user_name');
      if (storedName) {
        setUserName(storedName);
        console.log("Nome do usuário inicializado do localStorage no ClientDriverLayout:", storedName);
      } else if (session?.user?.name) {
        setUserName(session.user.name);
      }
    }
  }, [session]);

  // Adicionar um listener para detectar mudanças no localStorage
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'user_name' && e.newValue) {
        setUserName(e.newValue);
        console.log("Nome do usuário atualizado via localStorage no ClientDriverLayout:", e.newValue);
      }
    };

    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-40 border-b bg-background shadow-sm">
        <div className="container max-w-screen-2xl mx-auto px-2 sm:px-4 h-16 flex items-center justify-between py-2">
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="icon"
              className="mr-1 sm:mr-2 md:hidden"
              onClick={() => setSidebarOpen(!sidebarOpen)}
            >
              <Menu className="h-5 w-5" />
            </Button>
            <div className="flex items-center">
              <Link
                href="/driver/dashboard"
                className="flex items-center space-x-2 py-1 transition-colors hover:text-primary"
              >
                <div className="bg-primary/10 p-1.5 rounded-md flex-shrink-0">
                  <Truck className="h-5 w-5 text-primary" />
                </div>
                <span className="font-bold text-base hidden sm:inline-block truncate">
                  {userName ? `Viken Tours Sjåfør | ${userName}` : "Viken Tours Sjåfør"}
                </span>
                <span className="font-bold text-base sm:hidden">Sjåfør</span>
                {userName && (
                  <span className="ml-2 text-sm text-muted-foreground hidden sm:inline-block">
                    | {userName}
                  </span>
                )}
              </Link>
            </div>
          </div>

          {/* Høyre side */}
          <div className="flex items-center gap-2">
            <Link
              href="/driver/profile"
              className="hidden sm:flex items-center px-3 py-1.5 text-sm font-medium rounded-md hover:bg-accent hover:text-accent-foreground transition-colors mr-2"
            >
              <User className="mr-2 h-4 w-4" />
              <span>Min Profil</span>
            </Link>
            <ThemeToggle />
            <AdminUserNav
              user={{
                name: userName || session?.user?.name || "",
                email: session?.user?.email || "",
                image: null,
                role: "DRIVER", // Garantir que o papel seja sempre DRIVER neste layout
              }}
            />
          </div>
        </div>
      </header>

      {/* Mobil sidefelt-overlegg */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-background/80 backdrop-blur-sm md:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Mobil sidefelt */}
      <div
        data-sidebar
        className={`fixed top-0 left-0 z-50 h-full w-[280px] xs:w-[320px] border-r bg-background p-4 xs:p-6 shadow-lg transition-transform duration-200 md:hidden ${
          sidebarOpen ? "translate-x-0" : "-translate-x-full"
        }`}
      >
        <div className="flex items-center justify-between mb-6">
          <Link
            href="/driver/profile"
            className="block rounded-lg p-2 transition-colors hover:bg-accent"
            onClick={() => setSidebarOpen(false)}
          >
            <div className="flex items-center space-x-2">
              <div className="relative h-9 w-9 rounded-full bg-primary/10 flex items-center justify-center">
                <Truck className="h-5 w-5 text-primary" />
              </div>
              <div className="flex flex-col">
                <span className="text-lg font-semibold">
                  {userName
                    ? userName
                    : "Viken Tours Sjåfør"}
                </span>
                <span className="text-xs font-medium text-blue-600 dark:text-blue-500">
                  Brukeren min
                </span>
              </div>
            </div>
          </Link>
        </div>
        <DriverNav onLinkClick={() => setSidebarOpen(false)} />
      </div>

      <div className="flex-1 md:grid md:grid-cols-[220px_1fr]">
        <aside className="hidden md:block border-r bg-background">
          <div className="h-full py-6 pl-4 pr-6">
            <div className="mb-6">
              <Link
                href="/driver/profile"
                className="block rounded-lg p-2 transition-colors hover:bg-accent"
              >
                <div className="flex items-center space-x-2">
                  <div className="relative h-9 w-9 rounded-full bg-primary/10 flex items-center justify-center">
                    <Truck className="h-5 w-5 text-primary" />
                  </div>
                  <div className="flex flex-col">
                    <span className="text-lg font-semibold">
                      {userName
                        ? userName
                        : "Viken Tours Sjåfør"}
                    </span>
                    <span className="text-xs font-medium text-blue-600 dark:text-blue-500">
                      Brukeren min
                    </span>
                  </div>
                </div>
              </Link>
            </div>
            {/* onLinkClick-funksjonen er ikke nødvendig her, siden sidefeltet ikke lukkes på skrivebordet */}
            <DriverNav onLinkClick={() => {}} />
          </div>
        </aside>
        <main className="flex-1">
          <div className="container max-w-screen-2xl mx-auto p-2 sm:p-4">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
