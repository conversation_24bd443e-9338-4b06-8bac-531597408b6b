import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { db } from "@/lib/db";
import { updateDriverInfoInBookings } from "@/lib/mock-data";
import { updateDriverProfile } from "@/lib/data-fetching-mock";
import { useMockData } from "@/lib/use-mock-data";

export async function GET() {
  try {
    const session = await auth();

    if (!session || !session.user || !session.user.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    // Sjekk om brukeren er en sjåfør
    if (session.user.role !== "DRIVER") {
      return NextResponse.json({ message: "Forbidden" }, { status: 403 });
    }

    // Hent sjåførens profil
    const driverProfile = await db.driverProfile.findUnique({
      where: { userId: session.user.id },
    });

    // Hent brukeren
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        name: true,
        email: true,
        phone: true,
        role: true,
        image: true,
      },
    });

    return NextResponse.json({
      user,
      driverProfile: driverProfile || {},
    });
  } catch (error) {
    console.error("Error fetching driver profile:", error);
    return NextResponse.json(
      { message: "Internal Server Error" },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const session = await auth();

    if (!session || !session.user || !session.user.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    // Sjekk om brukeren er en sjåfør
    if (session.user.role !== "DRIVER") {
      return NextResponse.json({ message: "Forbidden" }, { status: 403 });
    }

    const data = await request.json();
    console.log("Received profile data:", data);

    // Verificar se estamos usando dados mockados
    if (useMockData()) {
      console.log("Using mock data for driver profile update");

      // Usar a função updateDriverProfile para atualizar o perfil do motorista nos dados mockados
      const result = await updateDriverProfile(session.user.id, {
        name: data.name,
        phone: data.phone,
        image: data.image,
        licenseNumber: data.licenseNumber || "",
        licenseType: data.licenseType || "",
        experienceYears: data.experienceYears
          ? parseInt(data.experienceYears)
          : 0,
        preferredVehicleType: data.preferredVehicleType || "",
        availableWeekends: data.availableWeekends === true,
        availableNights: data.availableNights === true,
        emergencyContactName: data.emergencyContactName || "",
        emergencyContactPhone: data.emergencyContactPhone || "",
        bio: data.bio || "",
        address: data.address || "",
        postalCode: data.postalCode || "",
        city: data.city || "",
      });

      if (!result.success) {
        return NextResponse.json(
          {
            message: "Kunne ikke oppdatere profilen",
            error: result.error,
          },
          { status: 400 }
        );
      }

      // Atualizar as informações do motorista em todas as corridas
      // Garantir que a imagem seja passada corretamente
      const driverInfo = {
        name: data.name,
        phone: data.phone || "",
        image: data.image || "", // Sempre passar a imagem, mesmo que seja vazia
        // Adicionar todos os campos do perfil para garantir que sejam atualizados
        driverProfile: {
          licenseNumber: data.licenseNumber || "",
          licenseType: data.licenseType || "",
          experienceYears: data.experienceYears
            ? parseInt(data.experienceYears)
            : 0,
          preferredVehicleType: data.preferredVehicleType || "",
          availableWeekends: data.availableWeekends === true,
          availableNights: data.availableNights === true,
          emergencyContactName: data.emergencyContactName || "",
          emergencyContactPhone: data.emergencyContactPhone || "",
          bio: data.bio || "",
          address: data.address || "",
          postalCode: data.postalCode || "",
          city: data.city || "",
        },
      };

      console.log(`API - Full driver info being updated:`, driverInfo);

      if (data.image) {
        console.log(
          `API - Updating driver image: ${data.image.substring(0, 30)}...`
        );
      }

      // Atualizar as informações em todas as corridas
      updateDriverInfoInBookings("driver1", driverInfo);

      return NextResponse.json({
        message: "Profile updated successfully",
        user: {
          id: session.user.id,
          name: data.name,
          email: session.user.email,
          phone: data.phone,
          image: data.image,
        },
        driverProfile: {
          userId: session.user.id,
          licenseNumber: data.licenseNumber || "",
          licenseType: data.licenseType || "",
          experienceYears: data.experienceYears
            ? parseInt(data.experienceYears)
            : 0,
          preferredVehicleType: data.preferredVehicleType || "",
          availableWeekends: data.availableWeekends === true,
          availableNights: data.availableNights === true,
          emergencyContactName: data.emergencyContactName || "",
          emergencyContactPhone: data.emergencyContactPhone || "",
          bio: data.bio || "",
          address: data.address || "",
          postalCode: data.postalCode || "",
          city: data.city || "",
        },
      });
    }

    // Oppdater brukeren, inkludert profilbilde
    // Não atualizamos o email, pois ele é único e não deve ser alterado
    const updatedUser = await db.user.update({
      where: { id: session.user.id },
      data: {
        name: data.name,
        // Removido: email: data.email, - não atualizamos o email
        phone: data.phone,
        image: data.image, // Legg til profilbildet
      },
    });

    console.log("Updated user:", updatedUser);

    // Sjekk om sjåførprofilen allerede finnes
    const existingProfile = await db.driverProfile.findUnique({
      where: { userId: session.user.id },
    });

    let driverProfile;

    // Forbered profildataene, håndter null- eller udefinerte verdier
    const profileData = {
      address: data.address || "",
      postalCode: data.postalCode || "",
      city: data.city || "",
      bio: data.bio || "",
      licenseNumber: data.licenseNumber || "",
      licenseType: data.licenseType || "",
      experienceYears: data.experienceYears
        ? parseInt(data.experienceYears)
        : 0,
      preferredVehicleType: data.preferredVehicleType || "",
      availableWeekends: data.availableWeekends === true,
      availableNights: data.availableNights === true,
      emergencyContactName: data.emergencyContactName || "",
      emergencyContactPhone: data.emergencyContactPhone || "",
    };

    console.log("Profile data to save:", profileData);

    if (existingProfile) {
      console.log("Updating existing profile for user:", session.user.id);
      // Oppdater eksisterende profil
      driverProfile = await db.driverProfile.update({
        where: { userId: session.user.id },
        data: profileData,
      });
    } else {
      console.log("Creating new profile for user:", session.user.id);
      // Opprett en ny profil
      driverProfile = await db.driverProfile.create({
        data: {
          userId: session.user.id,
          ...profileData,
        },
      });
    }

    console.log("Driver profile saved:", driverProfile);

    // Atualizar informações do motorista em todas as corridas mockadas
    // Isso garante que as corridas exibam as informações atualizadas do motorista
    console.log("Updating driver info in mock bookings");
    console.log("Driver ID:", session.user.id);
    console.log("Driver Name:", updatedUser.name);
    console.log("Driver Phone:", updatedUser.phone || "");
    console.log("Driver Image:", updatedUser.image || "");

    // Atualizar as informações do motorista em todas as corridas
    updateDriverInfoInBookings("driver1", {
      name: updatedUser.name,
      phone: updatedUser.phone || "",
      image: updatedUser.image || "",
    });

    return NextResponse.json({
      message: "Profile updated successfully",
      user: updatedUser,
      driverProfile,
    });
  } catch (error) {
    console.error("Error updating driver profile:", error);

    // Verificar se é um erro de violação de restrição única (como telefone duplicado)
    if (
      error instanceof Error &&
      error.message.includes("Unique constraint failed")
    ) {
      return NextResponse.json(
        {
          message: "Kunne ikke oppdatere profilen",
          error:
            "Telefonnummeret er allerede i bruk av en annen bruker. Vennligst bruk et annet telefonnummer.",
        },
        { status: 400 }
      );
    }

    // Outros erros
    return NextResponse.json(
      {
        message: "Kunne ikke oppdatere profilen",
        error: error instanceof Error ? error.message : String(error),
        details: "Kontakt systemadministratoren hvis problemet vedvarer.",
      },
      { status: 500 }
    );
  }
}
