"use client";

import { useState } from "react";
import Link from "next/link";
import { DashboardNav } from "@/components/dashboard-nav";
import { AdminNav } from "@/components/admin-nav";
import { AdminUserNav } from "@/components/admin-user-nav";
import { ThemeToggle } from "@/components/theme-toggle";
import { HelpPanel } from "@/components/help-panel";
import { NotificationPanel } from "@/components/notification-panel";
import {
  NotificationProvider,
  useNotifications,
} from "@/contexts/notification-context";
import { Menu, X, LayoutDashboard } from "lucide-react";
import { Button } from "@/components/ui/button";

interface ClientAdminLayoutProps {
  children: React.ReactNode;
  session: {
    user: {
      name?: string;
      email?: string;
      role?: string;
    };
    pendingBookings?: number;
  };
}

function NotificationPanelWrapper() {
  const { notifications, markAsRead, markAllAsRead, dismissNotification } =
    useNotifications();

  return (
    <NotificationPanel
      notifications={notifications}
      onMarkAsRead={markAsRead}
      onMarkAllAsRead={markAllAsRead}
      onDismiss={dismissNotification}
    />
  );
}

export default function ClientAdminLayout({
  children,
  session,
}: ClientAdminLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <NotificationProvider>
      <div className="flex min-h-screen flex-col">
        <header className="sticky top-0 z-40 border-b bg-background shadow-sm">
          <div className="container max-w-screen-2xl mx-auto px-2 sm:px-4 h-16 flex items-center justify-between py-2">
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="icon"
                className="mr-1 sm:mr-2 md:hidden"
                onClick={() => setSidebarOpen(!sidebarOpen)}
              >
                <Menu className="h-5 w-5" />
              </Button>
              <AdminNav />
            </div>

            {/* Høyre side */}
            <div className="flex items-center gap-2">
              <NotificationPanelWrapper />
              <HelpPanel />
              <ThemeToggle />
              <AdminUserNav
                user={{
                  name: session?.user?.name || "",
                  email: session?.user?.email || "",
                  image: null,
                  role: session?.user?.role || "Administrator",
                }}
              />
            </div>
          </div>
        </header>

        {/* Mobil sidefelt-overlegg */}
        {sidebarOpen && (
          <div
            className="fixed inset-0 z-40 bg-background/80 backdrop-blur-sm md:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* Mobil sidefelt */}
        <div
          data-sidebar
          className={`fixed top-0 left-0 z-50 h-full w-[280px] xs:w-[320px] border-r bg-background p-4 xs:p-6 shadow-lg transition-transform duration-200 md:hidden ${
            sidebarOpen ? "translate-x-0" : "-translate-x-full"
          }`}
        >
          <div className="flex items-center justify-between mb-6">
            <Link
              href="/admin/profile"
              className="block rounded-lg p-2 transition-colors hover:bg-accent"
              onClick={() => setSidebarOpen(false)}
            >
              <div className="flex items-center space-x-2">
                <div className="relative h-9 w-9 rounded-full bg-primary/10 flex items-center justify-center">
                  <LayoutDashboard className="h-5 w-5 text-primary" />
                </div>
                <div className="flex flex-col">
                  <span className="text-lg font-semibold">
                    {session?.user?.name
                      ? session.user.name
                      : "Viken Tours Admin"}
                  </span>
                  <span className="text-xs font-medium text-blue-600 dark:text-blue-500">
                    Brukeren min
                  </span>
                </div>
              </div>
            </Link>
          </div>
          <DashboardNav onLinkClick={() => setSidebarOpen(false)} />
        </div>

        <div className="flex-1 md:grid md:grid-cols-[220px_1fr]">
          <aside className="hidden md:block border-r bg-background">
            <div className="h-full py-6 pl-4 pr-6">
              <div className="mb-6">
                <Link
                  href="/admin/profile"
                  className="block rounded-lg p-2 transition-colors hover:bg-accent"
                >
                  <div className="flex items-center space-x-2">
                    <div className="relative h-9 w-9 rounded-full bg-primary/10 flex items-center justify-center">
                      <LayoutDashboard className="h-5 w-5 text-primary" />
                    </div>
                    <div className="flex flex-col">
                      <span className="text-lg font-semibold">
                        {session?.user?.name
                          ? session.user.name
                          : "Viken Tours Admin"}
                      </span>
                      <span className="text-xs font-medium text-blue-600 dark:text-blue-500">
                        Brukeren min
                      </span>
                    </div>
                  </div>
                </Link>
              </div>
              <DashboardNav />
            </div>
          </aside>
          <main className="flex-1">
            <div className="container max-w-screen-2xl mx-auto p-2 sm:p-4">
              {children}
            </div>
          </main>
        </div>
      </div>
    </NotificationProvider>
  );
}
