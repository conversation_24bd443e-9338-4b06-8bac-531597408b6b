@tailwind base;
@tailwind components;
@tailwind utilities;

@import "../styles/dark-theme-improvements.css";
@import "../styles/range-input.css";


/*  Felles rullefelt-stiler        */

:root {
  /* <PERSON><PERSON><PERSON><PERSON> at rullefelt ikke påvirker layoutet */
  --scrollbar-width: 10px;
}

/* Forhindre at siden flytter seg når dropdown-menyer <PERSON> */
html {
  overflow-y: scroll;
}

body {
  width: 100vw;
  overflow-x: hidden;
  padding-right: 0 !important; /* Forhindre padding som legges til av noen biblioteker */
}

/* Tilpasset posisjonering for brukermenyen */
.dropdown-menu-custom {
  transform: translateX(-140px) !important;
  margin-top: 5px !important;
  position: fixed !important;
  right: auto !important;
  left: auto !important;
}

/* Responsiv posisjonering for brukermenyen */
@media (max-width: 768px) {
  .dropdown-menu-custom {
    transform: translateX(0) !important;
    right: -8px !important;
    left: auto !important;
    width: auto !important;
    min-width: 200px !important;
  }
}

/* Ekstra justeringer for små mobilskjermer */
@media (max-width: 480px) {
  .dropdown-menu-custom {
    transform: translateX(0) !important;
    right: -8px !important;
    max-width: 90vw !important;
    min-width: 200px !important;
  }
}

/* Globale rullefelt-stiler */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

/* WebKit (Chrome, Safari, Edge) */
::-webkit-scrollbar {
  width: var(--scrollbar-width);
  height: var(--scrollbar-width);
}
::-webkit-scrollbar-track { background: transparent; }
::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 5px;
  border: 2px solid transparent;   /* sikrer konstant bredde */
  background-clip: content-box;
}
::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
  background-clip: content-box;
}

/* Forhindre layout-shift når rullefelt dukker opp */
html {
  overflow-y: scroll;
  scrollbar-gutter: stable both-edges;
}
.overflow-auto,
.overflow-y-auto,
.overflow-x-auto,
.overflow-scroll,
.overflow-y-scroll,
.overflow-x-scroll {
  scrollbar-gutter: stable both-edges;
}


/*  RGB-variabler til gradienter   */

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}
@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}


/*  CSS-variabler som Tailwind bruker  */

@layer base {

  :root {
    --background:          0   0% 100%;   /* #FFFFFF */
    --foreground:          0   0%   6%;   /* #111111 */

    --card:                0   0% 100%;
    --card-foreground:     0   0%   6%;

    --popover:             0   0% 100%;
    --popover-foreground:  0   0%   6%;

    --primary:            25 100%  50%;   /* #FF6B00 (oransje) */
    --primary-foreground:  0   0% 100%;   /* hvit tekst */

    --secondary:           0   0%  96%;   /* #F5F5F5 – lys varm grå */
    --secondary-foreground:0   0%   6%;

    --muted:               0   0%  96%;
    --muted-foreground:    0   0%  45%;

    --accent:             42 100%  70%;   /* #FFD166 – gul highlight */
    --accent-foreground:   0   0%   6%;

    --destructive:         0  84%  60.2%;
    --destructive-foreground: 0 0% 98%;

    --border:              0   0%  89.8%;
    --input:               0   0%  89.8%;
    --ring:               25 100%  50%;

    /* palett til grafer/diagrammer */
    --chart-1:            25 100%  50%;   /* oransje */
    --chart-2:            42 100%  70%;   /* gul */
    --chart-3:           197  37%  24%;   /* mørk blå-grå */
    --chart-4:            12  76%  61%;   /* korall */
    --chart-5:           173  58%  39%;   /* teal */

    --radius: 0.5rem;
  }

  /* ─────  MØRKT TEMA   ───── */
  .dark {
    --background:         222.2 84%  4.9%;
    --foreground:         210   40% 98%;

    --card:               222.2 84%  6.9%;
    --card-foreground:    210   40% 98%;

    --popover:            222.2 84%  6.9%;
    --popover-foreground: 210   40% 98%;

    --primary:            217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary:          217.2 32.6% 17.5%;
    --secondary-foreground:210  40% 98%;

    --muted:              217.2 32.6% 17.5%;
    --muted-foreground:   215   20.2% 65.1%;

    --accent:             217.2 32.6% 17.5%;
    --accent-foreground:  210   40% 98%;

    --destructive:         0   62.8% 30.6%;
    --destructive-foreground:210 40% 98%;

    --border:             217.2 32.6% 17.5%;
    --input:              217.2 32.6% 17.5%;
    --ring:               224.3 76.3% 48%;

    --chart-1:            217.2 91.2% 59.8%;
    --chart-2:            262.1 83.3% 57.8%;
    --chart-3:            316.6 73.3% 52.5%;
    --chart-4:             19.9 83.9% 56.3%;
    --chart-5:             47.9 95.8% 53.1%;
  }
}


/*  Globale Tailwind-hjelpeklasser */

@layer base {
  * { @apply border-border; }

  body {
    @apply bg-background text-foreground;
    overflow-y: auto;
    scrollbar-gutter: stable;
  }
}
