"use client";

import { useState, useEffect } from "react";
import { ThemeToggle } from "@/components/theme-toggle";
import { AdminUserNav } from "@/components/admin-user-nav";
import { useSession } from "next-auth/react";
import {
  Menu,
  X,
  User,
  Calendar,
  Settings,
  LogOut,
  CreditCard,
} from "lucide-react";
import { CalendarClockIcon } from "@/components/icons/calendar-clock-icon";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { useRouter } from "next/navigation";

interface ClientCustomerLayoutProps {
  children: React.ReactNode;
}

// Definer navigasjonselementer spesifikke for kunden
const customerNavItems = [
  {
    title: "Bestill",
    href: "/customer/bookings/new",
    icon: Calendar,
  },
  {
    title: "Mine turer",
    href: "/customer/bookings",
    icon: CalendarClockIcon,
  },
  {
    title: "Betaling",
    href: "/customer/payments",
    icon: CreditCard,
  },
  {
    title: "Innstillinger",
    href: "/customer/settings",
    icon: Settings,
  },
];

// Definer grensesnittet for CustomerNav-props
interface CustomerNavProps {
  onLinkClick: () => void;
}

// Navigasjonskomponent spesifikk for kunden
function CustomerNav({ onLinkClick }: CustomerNavProps) {
  return (
    <div className="space-y-4">
      <div className="px-3 py-2">
        <nav className="grid items-start gap-1.5">
          {customerNavItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className="group flex items-center rounded-md px-3 py-2.5 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground"
              onClick={onLinkClick}
            >
              <item.icon className="mr-3 h-5 w-5" />
              <span>{item.title}</span>
            </Link>
          ))}
          <Link
            href="/auth/logout"
            className="group flex items-center rounded-md px-3 py-2.5 text-sm font-medium text-red-500 transition-colors hover:bg-red-100 hover:text-red-600 dark:hover:bg-red-900/20"
            onClick={onLinkClick}
          >
            <LogOut className="mr-3 h-5 w-5" />
            <span>Logg ut</span>
          </Link>
        </nav>
      </div>
    </div>
  );
}

export default function ClientCustomerLayout({
  children,
}: ClientCustomerLayoutProps) {
  const { data: session, status } = useSession();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [userName, setUserName] = useState("");
  const [userEmail, setUserEmail] = useState("");
  const [userRole, setUserRole] = useState("CUSTOMER");

  // Efeito para inicializar o nome do usuário a partir do localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedName = localStorage.getItem('user_name');
      if (storedName) {
        setUserName(storedName);
        console.log("Nome do usuário inicializado do localStorage:", storedName);
      }
    }
  }, []);

  // Efeito para atualizar os dados do usuário quando a sessão mudar
  useEffect(() => {
    // Função para atualizar o nome do usuário
    const updateUserInfo = () => {
      // Verificar primeiro se há um nome no localStorage (prioridade mais alta)
      const storedName = localStorage.getItem('user_name');

      if (storedName) {
        setUserName(storedName);
        console.log("Nome do usuário carregado do localStorage:", storedName);
      } else if (session?.user?.name) {
        setUserName(session.user.name);
        console.log("Nome do usuário carregado da sessão:", session.user.name);
      } else {
        setUserName("Kunde");
      }

      // Atualizar outros dados do usuário
      setUserEmail(session?.user?.email || "");
      setUserRole(session?.user?.role || "CUSTOMER");
    };

    // Chamar a função de atualização
    updateUserInfo();

    // Adicionar um listener para detectar mudanças no localStorage
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'user_name') {
        updateUserInfo();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [session]);

  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-40 border-b bg-background shadow-sm">
        <div className="container max-w-screen-2xl mx-auto px-2 sm:px-4 h-16 flex items-center justify-between py-2">
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="icon"
              className="mr-1 sm:mr-2 md:hidden"
              onClick={() => setSidebarOpen(!sidebarOpen)}
            >
              <Menu className="h-5 w-5" />
            </Button>
            <div className="flex items-center">
              <Link
                href="/customer/bookings/new"
                className="flex items-center space-x-2 py-1 transition-colors hover:text-primary"
              >
                <span className="font-bold text-base">Viken Tours</span>
              </Link>
            </div>
          </div>

          {/* Høyre side */}
          <div className="flex items-center gap-2">
            <ThemeToggle />
            <AdminUserNav
              user={{
                name: userName,
                email: userEmail,
                image: null,
                role: userRole,
              }}
            />
          </div>
        </div>
      </header>

      {/* Mobil sidefelt-overlegg */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-background/80 backdrop-blur-sm md:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Mobil sidefelt */}
      <div
        data-sidebar
        className={`fixed top-0 left-0 z-50 h-full w-[280px] xs:w-[320px] border-r bg-background p-4 xs:p-6 shadow-lg transition-transform duration-200 md:hidden ${
          sidebarOpen ? "translate-x-0" : "-translate-x-full"
        }`}
      >
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div className="flex flex-col">
              <Link
                href="/customer/profile"
                className="block rounded-lg p-2 transition-colors hover:bg-accent"
                onClick={() => setSidebarOpen(false)}
              >
                <div className="flex items-center space-x-2">
                  <div className="relative h-9 w-9 rounded-full bg-primary/10 flex items-center justify-center">
                    <User className="h-5 w-5 text-primary" />
                  </div>
                  <div className="flex flex-col">
                    <span className="text-lg font-semibold">
                      {userName || "Viken Tours"}
                    </span>
                    <span className="text-xs font-medium text-blue-600 dark:text-blue-500">
                      Brukeren min
                    </span>
                  </div>
                </div>
              </Link>
            </div>
          </div>
        </div>
        <CustomerNav onLinkClick={() => setSidebarOpen(false)} />
      </div>

      <div className="flex-1 md:grid md:grid-cols-[220px_1fr]">
        <aside className="hidden md:block border-r bg-background">
          <div className="h-full py-6 pl-4 pr-6">
            <div className="mb-6">
              <div className="flex flex-col">
                <Link
                  href="/customer/profile"
                  className="block rounded-lg p-2 transition-colors hover:bg-accent"
                >
                  <div className="flex items-center space-x-2">
                    <div className="relative h-9 w-9 rounded-full bg-primary/10 flex items-center justify-center">
                      <User className="h-5 w-5 text-primary" />
                    </div>
                    <div className="flex flex-col">
                      <span className="text-lg font-semibold">
                        {userName || "Viken Tours"}
                      </span>
                      <span className="text-xs font-medium text-blue-600 dark:text-blue-500">
                        Brukeren min
                      </span>
                    </div>
                  </div>
                </Link>
              </div>
            </div>
            {/* onLinkClick-funksjonen er ikke nødvendig her, siden sidefeltet ikke lukkes på skrivebordet */}
            <CustomerNav onLinkClick={() => {}} />
          </div>
        </aside>
        <main className="flex-1">
          <div className="container max-w-screen-2xl mx-auto p-2 sm:p-4">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
