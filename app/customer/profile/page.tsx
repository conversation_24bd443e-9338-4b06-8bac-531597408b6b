"use client";

import { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";
import { useToast } from "@/components/ui/use-toast";
import { DashboardHeader } from "@/components/dashboard-header";
import { DashboardShell } from "@/components/dashboard-shell";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  User,
  Phone,
  MapPin,
  Mail,
  Save,
  Loader2,
  AlertCircle,
  Home,
} from "lucide-react";

export default function CustomerProfilePage() {
  const { data: session, update } = useSession();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [profileData, setProfileData] = useState({
    name: "",
    email: "",
    phone: "",
    address: "",
    postalCode: "",
    city: "",
    country: "Norge",
  });

  // Definir a função fetchProfileData com useCallback
  const fetchProfileData = useCallback(async () => {
    try {
      const response = await fetch("/api/user/profile");

      if (!response.ok) {
        throw new Error("Kunne ikke hente profildata");
      }

      const data = await response.json();
      setProfileData({
        name: data.name || "",
        email: data.email || "",
        phone: data.phone || "",
        address: data.address || "",
        postalCode: data.postalCode || "",
        city: data.city || "",
        country: data.country || "Norge",
      });
    } catch (error) {
      console.error("Error fetching profile data:", error);
      toast({
        title: "Feil",
        description: "Kunne ikke hente profildata",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast, setProfileData, setIsLoading]);

  // Carregar dados do perfil
  useEffect(() => {
    if (session?.user) {
      fetchProfileData();
    } else {
      setIsLoading(false);
    }
  }, [session, fetchProfileData]);

  // Manipular mudanças nos campos
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setProfileData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Salvar alterações no perfil
  const handleSaveProfile = async () => {
    setIsSaving(true);
    try {
      const response = await fetch("/api/user/profile", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(profileData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Kunne ikke oppdatere profil");
      }

      // Atualizar a sessão com o novo nome
      await update({
        ...session,
        user: {
          ...session?.user,
          name: profileData.name,
        },
      });

      // Armazenar o novo nome no localStorage para garantir que ele esteja disponível após o recarregamento
      localStorage.setItem('user_name', profileData.name);

      // Disparar um evento de storage para notificar outros componentes
      window.dispatchEvent(new StorageEvent('storage', {
        key: 'user_name',
        newValue: profileData.name,
        storageArea: localStorage
      }));

      // Mostrar mensagem de sucesso
      toast({
        title: "Profil oppdatert",
        description: "Din profilinformasjon har blitt oppdatert",
      });

      // Adicionar um pequeno atraso antes de recarregar a página
      await new Promise(resolve => setTimeout(resolve, 500));

      // Forçar um recarregamento completo da página
      window.location.reload();

      // Nota: Um segundo toast não será exibido devido ao reload da página
    } catch (error) {
      console.error("Error updating profile:", error);
      toast({
        title: "Feil",
        description:
          error instanceof Error
            ? error.message
            : "Kunne ikke oppdatere profil",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Se não houver sessão, mostrar mensagem de login
  if (!session) {
    return (
      <DashboardShell>
        <DashboardHeader
          heading="Profil"
          text="Du må logge inn for å se denne siden"
        />
      </DashboardShell>
    );
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Profil"
        text="Se og rediger din profilinformasjon"
      />

      {isLoading ? (
        <div className="flex items-center justify-center h-40">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="mr-2 h-5 w-5 text-primary" />
              Profilinformasjon
            </CardTitle>
            <CardDescription>
              Oppdater din personlige informasjon og adresse
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Personlig informasjon */}
              <div>
                <h3 className="text-lg font-medium mb-4">
                  Personlig informasjon
                </h3>
                <div className="space-y-4">
                  <div className="grid gap-2">
                    <Label htmlFor="name">Navn</Label>
                    <div className="relative">
                      <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="name"
                        name="name"
                        value={profileData.name}
                        onChange={handleChange}
                        className="pl-10"
                      />
                    </div>
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="email">E-post</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="email"
                        name="email"
                        value={profileData.email}
                        readOnly
                        className="pl-10 bg-muted"
                      />
                    </div>
                    <p className="text-xs text-muted-foreground">
                      E-postadressen kan ikke endres
                    </p>
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="phone">Telefon</Label>
                    <div className="relative">
                      <Phone className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="phone"
                        name="phone"
                        value={profileData.phone}
                        onChange={handleChange}
                        placeholder="+47 123 45 678"
                        className="pl-10"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Adresse */}
              <div>
                <h3 className="text-lg font-medium mb-4">Adresse</h3>
                <div className="space-y-4">
                  <div className="grid gap-2">
                    <Label htmlFor="address">Adresse</Label>
                    <div className="relative">
                      <Home className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="address"
                        name="address"
                        value={profileData.address}
                        onChange={handleChange}
                        placeholder="Gateadresse"
                        className="pl-10"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="grid gap-2">
                      <Label htmlFor="postalCode">Postnummer</Label>
                      <Input
                        id="postalCode"
                        name="postalCode"
                        value={profileData.postalCode}
                        onChange={handleChange}
                        placeholder="0000"
                      />
                    </div>

                    <div className="grid gap-2">
                      <Label htmlFor="city">Sted</Label>
                      <Input
                        id="city"
                        name="city"
                        value={profileData.city}
                        onChange={handleChange}
                        placeholder="Oslo"
                      />
                    </div>
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="country">Land</Label>
                    <div className="relative">
                      <MapPin className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="country"
                        name="country"
                        value={profileData.country}
                        onChange={handleChange}
                        placeholder="Norge"
                        className="pl-10"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-end border-t pt-4">
            <Button onClick={handleSaveProfile} disabled={isSaving}>
              {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Lagrer...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Lagre endringer
                </>
              )}
            </Button>
          </CardFooter>
        </Card>
      )}
    </DashboardShell>
  );
}
