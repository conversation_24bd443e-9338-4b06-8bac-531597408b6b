# Apresentação do Projeto Viken Tours

## Sistema de Gestão de Transporte e Turismo

---

## 📋 Índice

1. [Visão Geral do Projeto](#visão-geral)
2. [Tecnologias Utilizadas](#tecnologias)
3. [Arquitetura do Sistema](#arquitetura)
4. [Metodologia de Desenvolvimento](#metodologia)
5. [Funcionalidades Implementadas](#funcionalidades)
6. [Base de Dados](#base-de-dados)
7. [Como o Sistema Funciona](#funcionamento)
8. [Problemas Conhecidos](#problemas)
9. [Melhorias Necessárias](#melhorias)
10. [Demonstração](#demonstração)

---

## 🎯 Visão Geral do Projeto {#visão-geral}

### Objetivo

O **Viken Tours** é um sistema completo de gestão de transporte e turismo desenvolvido para a região de Viken, Noruega. O projeto visa digitalizar e otimizar o processo de reservas de transporte, gestão de motoristas e administração de veículos.

### Contexto Acadêmico

- **Tipo**: Projeto de faculdade
- **Finalidade**: Demonstrar competências em desenvolvimento full-stack
- **Escopo**: Sistema web completo com múltiplos tipos de usuários

### Principais Características

- Sistema multi-tenant com 3 tipos de usuários (Admin, Motorista, Cliente)
- Interface responsiva e moderna
- Gestão completa de reservas e agendamentos
- Dashboard analítico com relatórios
- Sistema de autenticação robusto

---

## 🛠️ Tecnologias Utilizadas {#tecnologias}

### Frontend

- **Next.js 14**: Framework React com App Router

  - _Motivo_: SSR/SSG para melhor performance e SEO
  - _Vantagem_: Roteamento automático e otimizações built-in

- **TypeScript**: Linguagem de programação

  - _Motivo_: Type safety e melhor experiência de desenvolvimento
  - _Vantagem_: Redução de bugs e melhor IntelliSense

- **TailwindCSS**: Framework CSS utility-first

  - _Motivo_: Desenvolvimento rápido e consistente
  - _Vantagem_: Design system integrado e responsividade

- **shadcn/ui**: Biblioteca de componentes
  - _Motivo_: Componentes acessíveis e customizáveis
  - _Vantagem_: Baseado em Radix UI com design moderno

### Backend

- **Next.js API Routes**: Backend integrado

  - _Motivo_: Full-stack em uma única aplicação
  - _Vantagem_: Compartilhamento de tipos e código

- **Prisma**: ORM (Object-Relational Mapping)

  - _Motivo_: Type-safe database access
  - _Vantagem_: Migrations automáticas e schema declarativo

- **NextAuth.js**: Sistema de autenticação
  - _Motivo_: Solução robusta e segura
  - _Vantagem_: Suporte a múltiplos providers

### Base de Dados

- **PostgreSQL**: Base de dados relacional

  - _Motivo_: Robustez e escalabilidade
  - _Vantagem_: ACID compliance e performance

- **Docker**: Containerização
  - _Motivo_: Ambiente consistente de desenvolvimento
  - _Vantagem_: Fácil setup e deployment

### Ferramentas Adicionais

- **Chart.js/Recharts**: Visualização de dados
- **React Hook Form**: Gestão de formulários
- **Zod**: Validação de schemas
- **Lucide React**: Ícones
- **MinIO**: Armazenamento de arquivos

---

## 🏗️ Arquitetura do Sistema {#arquitetura}

### Estrutura de Pastas

```
viken-tours/
├── app/                    # App Router (Next.js 14)
│   ├── admin/             # Páginas do administrador
│   ├── customer/          # Páginas do cliente
│   ├── driver/            # Páginas do motorista
│   ├── auth/              # Autenticação
│   └── api/               # API Routes
├── components/            # Componentes reutilizáveis
│   ├── ui/               # Componentes base (shadcn/ui)
│   ├── charts/           # Componentes de gráficos
│   └── forms/            # Componentes de formulários
├── lib/                  # Utilitários e configurações
├── prisma/               # Schema e migrações da BD
└── hooks/                # Custom React hooks
```

### Padrões Arquiteturais

#### 1. **Separation of Concerns**

- Separação clara entre UI, lógica de negócio e acesso a dados
- Componentes especializados por funcionalidade

#### 2. **Component-Based Architecture**

- Componentes reutilizáveis e modulares
- Props tipadas com TypeScript

#### 3. **Server-Side Rendering (SSR)**

- Páginas renderizadas no servidor para melhor performance
- Hidratação controlada no cliente

#### 4. **API-First Design**

- API REST bem estruturada
- Endpoints organizados por funcionalidade

### Fluxo de Dados

```
Cliente → Next.js Frontend → API Routes → Prisma → PostgreSQL
                ↓
        Componentes React ← Server Components ← Data Fetching
```

---

## 📊 Metodologia de Desenvolvimento {#metodologia}

### Abordagem Utilizada: **Agile/Scrum Adaptado**

#### Características Implementadas:

1. **Desenvolvimento Iterativo**

   - Funcionalidades desenvolvidas em sprints curtos
   - Feedback contínuo e melhorias incrementais

2. **User Stories**

   - Funcionalidades definidas do ponto de vista do usuário
   - Critérios de aceitação claros

3. **MVP (Minimum Viable Product)**
   - Foco nas funcionalidades essenciais primeiro
   - Expansão gradual das features

#### Fases de Desenvolvimento:

**Sprint 1: Fundação**

- Setup do projeto e tecnologias
- Autenticação básica
- Estrutura de base de dados

**Sprint 2: Core Features**

- Sistema de reservas
- Dashboards básicos
- CRUD de entidades principais

**Sprint 3: Melhorias**

- Interface responsiva
- Relatórios e analytics
- Otimizações de performance

**Sprint 4: Polimento**

- Testes e correções
- Documentação
- Deploy e configuração

### Práticas de Desenvolvimento:

#### **Code Quality**

- TypeScript para type safety
- ESLint para padronização de código
- Prettier para formatação consistente

#### **Version Control**

- Git com commits semânticos
- Branches por feature
- Pull requests para code review

#### **Testing Strategy**

- Testes unitários (planejados)
- Testes de integração (planejados)
- Testes manuais extensivos

---

## ⚙️ Funcionalidades Implementadas {#funcionalidades}

### 🔐 Sistema de Autenticação

- **Login/Logout seguro**
- **Gestão de sessões**
- **Proteção de rotas por role**
- **Middleware de autorização**

### 👨‍💼 Painel Administrativo

#### Dashboard Principal

- **Estatísticas em tempo real**

  - Total de reservas
  - Motoristas ativos
  - Veículos disponíveis
  - Reservas pendentes

- **Visualizações avançadas**
  - Gráficos de reservas ao longo do tempo
  - Distribuição por status
  - Análise de uso de veículos
  - KPIs customizáveis

#### Gestão de Entidades

- **Usuários**: CRUD completo com roles
- **Veículos**: Gestão de frota
- **Reservas**: Aprovação e acompanhamento
- **Relatórios**: Exportação de dados

### 🚗 Painel do Motorista

- **Dashboard personalizado**

  - Reservas do dia
  - Estatísticas pessoais
  - Histórico de viagens

- **Gestão de agenda**
  - Calendário de disponibilidade
  - Aceitar/rejeitar reservas
  - Atualização de status

### 👤 Painel do Cliente

- **Reservas de transporte**

  - Formulário de nova reserva
  - Seleção de veículo
  - Agendamento de data/hora

- **Histórico pessoal**
  - Reservas anteriores
  - Status atual
  - Avaliações

### 📊 Sistema de Relatórios

- **Analytics avançados**
- **Exportação de dados**
- **Gráficos interativos**
- **Métricas de performance**

---

## 🗄️ Base de Dados {#base-de-dados}

### Escolha do PostgreSQL

#### Motivos da Escolha:

1. **Robustez**: ACID compliance e transações seguras
2. **Escalabilidade**: Suporte a grandes volumes de dados
3. **Funcionalidades avançadas**: JSON support, full-text search
4. **Comunidade**: Amplo suporte e documentação
5. **Integração**: Excelente suporte no ecossistema Node.js

### Schema da Base de Dados

#### Entidades Principais:

**Users (Usuários)**

```sql
- id: String (CUID)
- email: String (unique)
- name: String
- phone: String?
- passwordHash: String
- role: String (CUSTOMER|DRIVER|ADMIN)
- image: String?
- createdAt/updatedAt: DateTime
```

**Vehicles (Veículos)**

```sql
- id: String (CUID)
- type: VehicleType (BUS|MINIBUS|MINIVAN|VIP_SEDAN)
- model: String
- plateNumber: String (unique)
- capacity: Int
- isAvailable: Boolean
```

**Bookings (Reservas)**

```sql
- id: String (CUID)
- customerId: String (FK)
- driverId: String? (FK)
- vehicleId: String (FK)
- status: BookingStatus
- pickupLocation: String
- dropoffLocation: String
- scheduledTime: DateTime
- passengers: Int
- specialRequests: String?
```

**Payments (Pagamentos)**

```sql
- id: String (CUID)
- bookingId: String (FK unique)
- amount: Float
- method: PaymentMethodType
- status: PaymentStatus
```

#### Relacionamentos:

- **User → Bookings**: 1:N (como cliente e motorista)
- **Vehicle → Bookings**: 1:N
- **Booking → Payment**: 1:1
- **User → DriverProfile**: 1:1
- **User → CustomerProfile**: 1:1

### Estratégia de Dados

#### **Prisma ORM**

- **Schema declarativo** em `schema.prisma`
- **Migrations automáticas** para versionamento
- **Type-safe queries** com TypeScript
- **Relacionamentos tipados**

#### **Indexação**

- Índices em chaves estrangeiras
- Índices compostos para queries frequentes
- Otimização de performance

---

## 🔄 Como o Sistema Funciona {#funcionamento}

### Fluxo Principal de Uso

#### 1. **Autenticação**

```
Usuário → Login → Verificação → Redirecionamento por Role
```

#### 2. **Reserva de Transporte (Cliente)**

```
Cliente → Dashboard → Nova Reserva → Seleção de Veículo →
Agendamento → Confirmação → Aguarda Aprovação
```

#### 3. **Gestão de Reservas (Admin)**

```
Admin → Dashboard → Reservas Pendentes → Análise →
Atribuição de Motorista → Aprovação → Notificação
```

#### 4. **Execução da Viagem (Motorista)**

```
Motorista → Dashboard → Reservas do Dia → Aceitar →
Iniciar Viagem → Atualizar Status → Finalizar
```

### Arquitetura de Componentes

#### **Server Components (Next.js 14)**

- Renderização no servidor
- Acesso direto à base de dados
- SEO otimizado

#### **Client Components**

- Interatividade no browser
- Estado local e hooks
- Componentes marcados com "use client"

#### **API Routes**

- Endpoints REST
- Validação com Zod
- Middleware de autenticação

### Sistema de Estados

#### **Reservas**

```
PENDING → CONFIRMED → IN_PROGRESS → COMPLETED
                  ↘ CANCELLED
```

#### **Pagamentos**

```
PENDING → COMPLETED
       ↘ FAILED → REFUNDED
```

### Gestão de Sessões

- **JWT tokens** via NextAuth.js
- **Session middleware** para proteção de rotas
- **Role-based access control**

---

## ⚠️ Problemas Conhecidos {#problemas}

### 1. **Problemas de Hidratação**

**Descrição**: Erros de hidratação em componentes interativos
**Causa**: Diferenças entre renderização server-side e client-side
**Status**: Parcialmente resolvido com componentes client-only
**Impacto**: Médio - afeta UX em alguns componentes

### 2. **Sistema de Pagamentos**

**Descrição**: Integração com gateways de pagamento não implementada
**Causa**: Complexidade e necessidade de credenciais reais
**Status**: Mockado/Simulado
**Impacto**: Alto - funcionalidade crítica não funcional

### 3. **Notificações em Tempo Real**

**Descrição**: Sistema de notificações push não implementado
**Causa**: Necessidade de WebSockets ou Server-Sent Events
**Status**: Não implementado
**Impacto**: Médio - afeta comunicação entre usuários

### 4. **Testes Automatizados**

**Descrição**: Cobertura de testes insuficiente
**Causa**: Foco no desenvolvimento de features
**Status**: Planejado mas não implementado
**Impacto**: Alto - qualidade e manutenibilidade

### 5. **Performance em Dispositivos Móveis**

**Descrição**: Carregamento lento em conexões lentas
**Causa**: Bundle size e falta de otimizações específicas
**Status**: Parcialmente otimizado
**Impacto**: Médio - afeta UX mobile

### 6. **Validação de Dados**

**Descrição**: Validação inconsistente entre frontend e backend
**Causa**: Schemas não sincronizados
**Status**: Em desenvolvimento
**Impacto**: Médio - pode causar erros de dados

### 7. **Gestão de Arquivos**

**Descrição**: Upload de imagens e documentos limitado
**Causa**: Integração com MinIO não completamente implementada
**Status**: Básico implementado
**Impacto**: Baixo - funcionalidade secundária

---

## 🚀 Melhorias Necessárias {#melhorias}

### Prioridade Alta

#### 1. **Sistema de Pagamentos Real**

- Integração com Stripe/PayPal
- Processamento seguro de cartões
- Gestão de reembolsos
- **Estimativa**: 2-3 semanas

#### 2. **Testes Automatizados**

- Testes unitários com Jest
- Testes de integração com Playwright
- Testes E2E automatizados
- **Estimativa**: 3-4 semanas

#### 3. **Otimização de Performance**

- Code splitting avançado
- Lazy loading de componentes
- Otimização de imagens
- Service Workers para cache
- **Estimativa**: 2 semanas

#### 4. **Sistema de Notificações**

- WebSockets para tempo real
- Push notifications
- Email notifications
- SMS integration
- **Estimativa**: 2-3 semanas

### Prioridade Média

#### 5. **Melhorias de UX/UI**

- Design system mais robusto
- Animações e transições
- Feedback visual melhorado
- Acessibilidade (WCAG 2.1)
- **Estimativa**: 3-4 semanas

#### 6. **Analytics Avançados**

- Google Analytics integration
- Custom event tracking
- Business intelligence dashboard
- Relatórios automatizados
- **Estimativa**: 2 semanas

#### 7. **Gestão de Arquivos Completa**

- Upload múltiplo
- Compressão de imagens
- Gestão de documentos
- Backup automático
- **Estimativa**: 1-2 semanas

#### 8. **API Melhorada**

- Rate limiting
- API versioning
- Documentação OpenAPI
- Webhooks
- **Estimativa**: 2 semanas

### Prioridade Baixa

#### 9. **Funcionalidades Avançadas**

- Chat interno
- Sistema de avaliações
- Programa de fidelidade
- Multi-idioma (i18n)
- **Estimativa**: 4-6 semanas

#### 10. **DevOps e Deploy**

- CI/CD pipeline
- Monitoring e logging
- Backup automatizado
- Scaling automático
- **Estimativa**: 2-3 semanas

#### 11. **Mobile App**

- React Native app
- Push notifications nativas
- Offline functionality
- **Estimativa**: 8-12 semanas

### Melhorias Técnicas

#### **Arquitetura**

- Microservices architecture
- Event-driven design
- CQRS pattern implementation
- GraphQL API

#### **Segurança**

- Penetration testing
- OWASP compliance
- Data encryption
- Audit logging

#### **Escalabilidade**

- Database sharding
- CDN implementation
- Load balancing
- Caching strategies

---

## 🎬 Demonstração {#demonstração}

### Credenciais de Acesso

#### Administrador

- **Email**: <EMAIL>
- **Senha**: admin12345
- **Funcionalidades**: Gestão completa do sistema

#### Motorista

- **Email**: <EMAIL>
- **Senha**: driver123
- **Funcionalidades**: Dashboard de motorista, gestão de viagens

#### Cliente

- **Email**: <EMAIL>
- **Senha**: customer123
- **Funcionalidades**: Reservas, histórico pessoal

### Fluxo de Demonstração

#### 1. **Setup Local**

```bash
# Clone do repositório
git clone [URL-DO-REPO]
cd viken-tours

# Instalação de dependências
npm install

# Setup automático
npm run setup

# Iniciar aplicação
npm run dev
```

#### 2. **Demonstração por Role**

**Admin Dashboard**:

- Estatísticas em tempo real
- Gestão de reservas
- Relatórios e analytics
- Gestão de usuários

**Driver Dashboard**:

- Reservas do dia
- Estatísticas pessoais
- Calendário de disponibilidade

**Customer Dashboard**:

- Nova reserva
- Histórico de viagens
- Status de reservas

### URLs Principais

- **Home**: http://localhost:3000
- **Login**: http://localhost:3000/auth/login
- **Admin**: http://localhost:3000/admin/dashboard
- **Driver**: http://localhost:3000/driver/dashboard
- **Customer**: http://localhost:3000/customer/dashboard

---

## 📈 Métricas do Projeto

### Estatísticas de Código

- **Linhas de código**: ~15,000+
- **Componentes React**: 80+
- **API Endpoints**: 25+
- **Páginas**: 20+
- **Modelos de dados**: 8

### Tecnologias e Dependências

- **Dependências principais**: 45+
- **DevDependencies**: 15+
- **Linguagens**: TypeScript (95%), JavaScript (5%)

### Performance

- **Lighthouse Score**: 85+ (estimado)
- **Bundle Size**: ~2MB (otimizável)
- **Load Time**: <3s (desenvolvimento)

---

## 🎯 Conclusão

### Objetivos Alcançados

✅ Sistema full-stack funcional
✅ Múltiplos tipos de usuários
✅ Interface moderna e responsiva
✅ Base de dados robusta
✅ Autenticação segura
✅ Dashboard analítico

### Aprendizados

- **Desenvolvimento full-stack** com Next.js
- **Gestão de estado** complexo
- **Design de base de dados** relacional
- **Arquitetura de componentes** escalável
- **Metodologias ágeis** de desenvolvimento

### Valor Acadêmico

Este projeto demonstra competências em:

- **Análise de requisitos**
- **Design de sistema**
- **Implementação técnica**
- **Gestão de projeto**
- **Resolução de problemas**

### Próximos Passos

1. Implementar melhorias de alta prioridade
2. Adicionar testes automatizados
3. Otimizar performance
4. Preparar para produção

---

**Desenvolvido por**: [Seu Nome]
**Período**: [Período de Desenvolvimento]
**Instituição**: [Nome da Faculdade]
**Curso**: [Nome do Curso]
