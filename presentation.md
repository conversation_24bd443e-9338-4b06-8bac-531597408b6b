# Presentasjon av Viken Tours Prosjektet

## Transport- og Turismestyringssystem

---

## 📋 Innholdsfortegnelse

1. [Prosjektoversikt](#prosjektoversikt)
2. [Teknologier som brukes](#teknologier)
3. [Systemarkitektur](#arkitektur)
4. [Utviklingsmetodikk](#metodikk)
5. [Implementerte funksjoner](#funksjoner)
6. [Database](#database)
7. [Hvordan systemet fungerer](#funksjon)
8. [<PERSON><PERSON><PERSON> problemer](#problemer)
9. [Nødvendige forbedringer](#forbedringer)
10. [Demonstrasjon](#demonstrasjon)

---

## 🎯 Prosjektoversikt {#prosjektoversikt}

### Mål

**Viken Tours** er et komplett transport- og turismestyringssystem utviklet for Viken-regionen i Norge. Prosjektet har som mål å digitalisere og optimalisere prosessen for transportbestillinger, sjåføradministrasjon og kjøretøystyring.

### Akademisk kontekst

- **Type**: Universitetsprosjekt
- **Formål**: Demonstrere kompetanse innen full-stack utvikling
- **Omfang**: Komplett websystem med flere brukertyper

### Hovedegenskaper

- Multi-tenant system med 3 brukertyper (Admin, Sjåfør, Kunde)
- Responsiv og moderne grensesnitt
- Komplett bestillings- og planleggingsstyring
- Analytisk dashboard med rapporter
- Robust autentiseringssystem

---

## 🛠️ Teknologier som brukes {#teknologier}

### Frontend

- **Next.js 14**: React-rammeverk med App Router

  - _Begrunnelse_: SSR/SSG for bedre ytelse og SEO
  - _Fordel_: Automatisk ruting og innebygde optimaliseringer

- **TypeScript**: Programmeringsspråk

  - _Begrunnelse_: Type safety og bedre utvikleropplevelse
  - _Fordel_: Reduserte bugs og bedre IntelliSense

- **TailwindCSS**: Utility-first CSS-rammeverk

  - _Begrunnelse_: Rask og konsistent utvikling
  - _Fordel_: Integrert design system og responsivitet

- **shadcn/ui**: Komponentbibliotek
  - _Begrunnelse_: Tilgjengelige og tilpassbare komponenter
  - _Fordel_: Basert på Radix UI med moderne design

### Backend

- **Next.js API Routes**: Integrert backend

  - _Begrunnelse_: Full-stack i én applikasjon
  - _Fordel_: Deling av typer og kode

- **Prisma**: ORM (Object-Relational Mapping)

  - _Begrunnelse_: Type-safe databasetilgang
  - _Fordel_: Automatiske migrasjoner og deklarativt skjema

- **NextAuth.js**: Autentiseringssystem
  - _Begrunnelse_: Robust og sikker løsning
  - _Fordel_: Støtte for flere providers

### Database

- **PostgreSQL**: Relasjonsdatabase

  - _Begrunnelse_: Robusthet og skalerbarhet
  - _Fordel_: ACID compliance og ytelse

- **Docker**: Containerisering
  - _Begrunnelse_: Konsistent utviklingsmiljø
  - _Fordel_: Enkel oppsett og deployment

### Tilleggsverktøy

- **Chart.js/Recharts**: Datavisualisering
- **React Hook Form**: Skjemahåndtering
- **Zod**: Skjemavalidering
- **Lucide React**: Ikoner
- **MinIO**: Fillagring

---

## 🏗️ Systemarkitektur {#arkitektur}

### Mappestruktur

```
viken-tours/
├── app/                    # App Router (Next.js 14)
│   ├── admin/             # Administratorsider
│   ├── customer/          # Kundesider
│   ├── driver/            # Sjåførsider
│   ├── auth/              # Autentisering
│   └── api/               # API Routes
├── components/            # Gjenbrukbare komponenter
│   ├── ui/               # Grunnkomponenter (shadcn/ui)
│   ├── charts/           # Diagramkomponenter
│   └── forms/            # Skjemakomponenter
├── lib/                  # Verktøy og konfigurasjoner
├── prisma/               # Database skjema og migrasjoner
└── hooks/                # Tilpassede React hooks
```

### Arkitektoniske mønstre

#### 1. **Separation of Concerns**

- Klar separasjon mellom UI, forretningslogikk og datatilgang
- Spesialiserte komponenter etter funksjonalitet

#### 2. **Komponentbasert arkitektur**

- Gjenbrukbare og modulære komponenter
- Typede props med TypeScript

#### 3. **Server-Side Rendering (SSR)**

- Sider rendret på serveren for bedre ytelse
- Kontrollert hydrering på klienten

#### 4. **API-First Design**

- Velstrukturert REST API
- Endepunkter organisert etter funksjonalitet

### Dataflyt

```
Klient → Next.js Frontend → API Routes → Prisma → PostgreSQL
                ↓
        React Komponenter ← Server Komponenter ← Data Fetching
```

---

## 📊 Utviklingsmetodikk {#metodikk}

### Tilnærming brukt: **Tilpasset Agile/Scrum**

#### Implementerte egenskaper:

1. **Iterativ utvikling**

   - Funksjoner utviklet i korte sprinter
   - Kontinuerlig tilbakemelding og inkrementelle forbedringer

2. **User Stories**

   - Funksjoner definert fra brukerens perspektiv
   - Klare akseptkriterier

3. **MVP (Minimum Viable Product)**
   - Fokus på essensielle funksjoner først
   - Gradvis utvidelse av features

#### Utviklingsfaser:

**Sprint 1: Fundament**

- Prosjektoppsett og teknologier
- Grunnleggende autentisering
- Database struktur

**Sprint 2: Kjernefunksjoner**

- Bestillingssystem
- Grunnleggende dashboards
- CRUD for hovedentiteter

**Sprint 3: Forbedringer**

- Responsivt grensesnitt
- Rapporter og analytics
- Ytelsesoptimaliseringer

**Sprint 4: Polering**

- Testing og rettelser
- Dokumentasjon
- Deploy og konfigurasjon

### Utviklingspraksis:

#### **Kodekvalitet**

- TypeScript for type safety
- ESLint for kodestandardisering
- Prettier for konsistent formatering

#### **Versjonskontroll**

- Git med semantiske commits
- Branches per feature
- Pull requests for kodegjennomgang

#### **Teststrategi**

- Enhetstester (planlagt)
- Integrasjonstester (planlagt)
- Omfattende manuell testing

---

## ⚙️ Implementerte funksjoner {#funksjoner}

### 🔐 Autentiseringssystem

- **Sikker innlogging/utlogging**
- **Sesjonshåndtering**
- **Rutebeskyttelse etter rolle**
- **Autorisasjonsmiddleware**

### 👨‍💼 Administratorpanel

#### Hoveddashboard

- **Sanntidsstatistikk**

  - Totale bestillinger
  - Aktive sjåfører
  - Tilgjengelige kjøretøy
  - Ventende bestillinger

- **Avanserte visualiseringer**
  - Diagrammer over bestillinger over tid
  - Statusfordeling
  - Kjøretøybruksanalyse
  - Tilpassbare KPIer

#### Entitetshåndtering

- **Brukere**: Komplett CRUD med roller
- **Kjøretøy**: Flåtestyring
- **Bestillinger**: Godkjenning og oppfølging
- **Rapporter**: Dataeksport

### 🚗 Sjåførpanel

- **Personlig dashboard**

  - Dagens bestillinger
  - Personlig statistikk
  - Reisehistorikk

- **Kalenderstyring**
  - Tilgjengelighetskalender
  - Aksepter/avvis bestillinger
  - Statusoppdateringer

### 👤 Kundepanel

- **Transportbestillinger**

  - Nytt bestillingsskjema
  - Kjøretøyvalg
  - Dato/tid-planlegging

- **Personlig historikk**
  - Tidligere bestillinger
  - Nåværende status
  - Vurderinger

### 📊 Rapportsystem

- **Avansert analytics**
- **Dataeksport**
- **Interaktive diagrammer**
- **Ytelsesmålinger**

---

## 🗄️ Database {#database}

### Valg av PostgreSQL

#### Begrunnelser for valget:

1. **Robusthet**: ACID compliance og sikre transaksjoner
2. **Skalerbarhet**: Støtte for store datamengder
3. **Avanserte funksjoner**: JSON-støtte, full-text søk
4. **Fellesskap**: Bred støtte og dokumentasjon
5. **Integrasjon**: Utmerket støtte i Node.js-økosystemet

### Database skjema

#### Hovedentiteter:

**Users (Brukere)**

```sql
- id: String (CUID)
- email: String (unique)
- name: String
- phone: String?
- passwordHash: String
- role: String (CUSTOMER|DRIVER|ADMIN)
- image: String?
- createdAt/updatedAt: DateTime
```

**Vehicles (Kjøretøy)**

```sql
- id: String (CUID)
- type: VehicleType (BUS|MINIBUS|MINIVAN|VIP_SEDAN)
- model: String
- plateNumber: String (unique)
- capacity: Int
- isAvailable: Boolean
```

**Bookings (Bestillinger)**

```sql
- id: String (CUID)
- customerId: String (FK)
- driverId: String? (FK)
- vehicleId: String (FK)
- status: BookingStatus
- pickupLocation: String
- dropoffLocation: String
- scheduledTime: DateTime
- passengers: Int
- specialRequests: String?
```

**Payments (Betalinger)**

```sql
- id: String (CUID)
- bookingId: String (FK unique)
- amount: Float
- method: PaymentMethodType
- status: PaymentStatus
```

#### Relasjoner:

- **User → Bookings**: 1:N (som kunde og sjåfør)
- **Vehicle → Bookings**: 1:N
- **Booking → Payment**: 1:1
- **User → DriverProfile**: 1:1
- **User → CustomerProfile**: 1:1

### Datastrategi

#### **Prisma ORM**

- **Deklarativt skjema** i `schema.prisma`
- **Automatiske migrasjoner** for versjonering
- **Type-safe spørringer** med TypeScript
- **Typede relasjoner**

#### **Indeksering**

- Indekser på fremmednøkler
- Sammensatte indekser for hyppige spørringer
- Ytelsesoptimalisering

---

## 🔄 Hvordan systemet fungerer {#funksjon}

### Hovedarbeidsflyt

#### 1. **Autentisering**

```
Bruker → Innlogging → Verifisering → Omdirigering etter rolle
```

#### 2. **Transportbestilling (Kunde)**

```
Kunde → Dashboard → Ny bestilling → Kjøretøyvalg →
Planlegging → Bekreftelse → Venter på godkjenning
```

#### 3. **Bestillingshåndtering (Admin)**

```
Admin → Dashboard → Ventende bestillinger → Analyse →
Sjåførtildeling → Godkjenning → Varsling
```

#### 4. **Turoppdrag (Sjåfør)**

```
Sjåfør → Dashboard → Dagens bestillinger → Aksepter →
Start tur → Oppdater status → Fullfør
```

### Komponentarkitektur

#### **Server Components (Next.js 14)**

- Rendering på serveren
- Direkte databasetilgang
- SEO-optimalisert

#### **Client Components**

- Interaktivitet i nettleseren
- Lokal tilstand og hooks
- Komponenter merket med "use client"

#### **API Routes**

- REST-endepunkter
- Validering med Zod
- Autentiseringsmiddleware

### Tilstandssystem

#### **Bestillinger**

```
PENDING → CONFIRMED → IN_PROGRESS → COMPLETED
                  ↘ CANCELLED
```

#### **Betalinger**

```
PENDING → COMPLETED
       ↘ FAILED → REFUNDED
```

### Sesjonshåndtering

- **JWT tokens** via NextAuth.js
- **Sesjonsmiddleware** for rutebeskyttelse
- **Rollebasert tilgangskontroll**

---

## ⚠️ Kjente problemer {#problemer}

### 1. **Hydreringsproblemer**

**Beskrivelse**: Hydreringsfeil i interaktive komponenter
**Årsak**: Forskjeller mellom server-side og client-side rendering
**Status**: Delvis løst med client-only komponenter
**Påvirkning**: Middels - påvirker UX i noen komponenter

### 2. **Betalingssystem**

**Beskrivelse**: Integrasjon med betalingsgateway ikke implementert
**Årsak**: Kompleksitet og behov for ekte legitimasjon
**Status**: Mockad/Simulert
**Påvirkning**: Høy - kritisk funksjonalitet ikke fungerende

### 3. **Sanntidsvarsler**

**Beskrivelse**: Push-varslingssystem ikke implementert
**Årsak**: Behov for WebSockets eller Server-Sent Events
**Status**: Ikke implementert
**Påvirkning**: Middels - påvirker kommunikasjon mellom brukere

### 4. **Automatiserte tester**

**Beskrivelse**: Utilstrekkelig testdekning
**Årsak**: Fokus på funksjonsutvikling
**Status**: Planlagt men ikke implementert
**Påvirkning**: Høy - kvalitet og vedlikeholdbarhet

### 5. **Ytelse på mobile enheter**

**Beskrivelse**: Langsom lasting på trege forbindelser
**Årsak**: Bundle-størrelse og mangel på spesifikke optimaliseringer
**Status**: Delvis optimalisert
**Påvirkning**: Middels - påvirker mobil UX

### 6. **Datavalidering**

**Beskrivelse**: Inkonsistent validering mellom frontend og backend
**Årsak**: Ikke-synkroniserte skjemaer
**Status**: Under utvikling
**Påvirkning**: Middels - kan forårsake datafeil

### 7. **Filhåndtering**

**Beskrivelse**: Begrenset opplasting av bilder og dokumenter
**Årsak**: MinIO-integrasjon ikke fullstendig implementert
**Status**: Grunnleggende implementert
**Påvirkning**: Lav - sekundær funksjonalitet

---

## 🚀 Nødvendige forbedringer {#forbedringer}

### Høy prioritet

#### 1. **Ekte betalingssystem**

- Integrasjon med Stripe/PayPal
- Sikker kortbehandling
- Refusjonshåndtering
- **Estimat**: 2-3 uker

#### 2. **Automatiserte tester**

- Enhetstester med Jest
- Integrasjonstester med Playwright
- Automatiserte E2E-tester
- **Estimat**: 3-4 uker

#### 3. **Ytelsesoptimalisering**

- Avansert code splitting
- Lazy loading av komponenter
- Bildeoptimalisering
- Service Workers for cache
- **Estimat**: 2 uker

#### 4. **Varslingssystem**

- WebSockets for sanntid
- Push-varsler
- E-postvarsler
- SMS-integrasjon
- **Estimat**: 2-3 uker

### Middels prioritet

#### 5. **UX/UI-forbedringer**

- Mer robust design system
- Animasjoner og overganger
- Forbedret visuell tilbakemelding
- Tilgjengelighet (WCAG 2.1)
- **Estimat**: 3-4 uker

#### 6. **Avansert analytics**

- Google Analytics-integrasjon
- Tilpasset hendelsesporing
- Business intelligence dashboard
- Automatiserte rapporter
- **Estimat**: 2 uker

#### 7. **Komplett filhåndtering**

- Flere opplastinger
- Bildekomprimering
- Dokumenthåndtering
- Automatisk backup
- **Estimat**: 1-2 uker

#### 8. **Forbedret API**

- Rate limiting
- API-versjonering
- OpenAPI-dokumentasjon
- Webhooks
- **Estimat**: 2 uker

### Lav prioritet

#### 9. **Avanserte funksjoner**

- Intern chat
- Vurderingssystem
- Lojalitetsprogram
- Flerspråklig (i18n)
- **Estimat**: 4-6 uker

#### 10. **DevOps og deployment**

- CI/CD pipeline
- Overvåking og logging
- Automatisk backup
- Automatisk skalering
- **Estimat**: 2-3 uker

#### 11. **Mobilapp**

- React Native app
- Native push-varsler
- Offline-funksjonalitet
- **Estimat**: 8-12 uker

### Tekniske forbedringer

#### **Arkitektur**

- Microservices-arkitektur
- Event-driven design
- CQRS-mønsterimplementering
- GraphQL API

#### **Sikkerhet**

- Penetrasjonstesting
- OWASP-compliance
- Datakryptering
- Audit logging

#### **Skalerbarhet**

- Database sharding
- CDN-implementering
- Load balancing
- Caching-strategier

---

## 🎬 Demonstrasjon {#demonstrasjon}

### Tilgangslegitimering

#### Administrator

- **E-post**: <EMAIL>
- **Passord**: admin12345
- **Funksjoner**: Komplett systemhåndtering

#### Sjåfør

- **E-post**: <EMAIL>
- **Passord**: driver123
- **Funksjoner**: Sjåførdashboard, turhåndtering

#### Kunde

- **E-post**: <EMAIL>
- **Passord**: customer123
- **Funksjoner**: Bestillinger, personlig historikk

### Demonstrasjonsflyt

#### 1. **Lokal oppsett**

```bash
# Klon repository
git clone [REPO-URL]
cd viken-tours

# Installer avhengigheter
npm install

# Automatisk oppsett
npm run setup

# Start applikasjon
npm run dev
```

#### 2. **Demonstrasjon per rolle**

**Admin Dashboard**:

- Sanntidsstatistikk
- Bestillingshåndtering
- Rapporter og analytics
- Brukerhåndtering

**Sjåfør Dashboard**:

- Dagens bestillinger
- Personlig statistikk
- Tilgjengelighetskalender

**Kunde Dashboard**:

- Ny bestilling
- Reisehistorikk
- Bestillingsstatus

### Hoved-URLer

- **Hjem**: http://localhost:3000
- **Innlogging**: http://localhost:3000/auth/login
- **Admin**: http://localhost:3000/admin/dashboard
- **Sjåfør**: http://localhost:3000/driver/dashboard
- **Kunde**: http://localhost:3000/customer/dashboard

---

## 📈 Prosjektmålinger

### Kodestatistikk

- **Kodelinjer**: ~15,000+
- **React-komponenter**: 80+
- **API-endepunkter**: 25+
- **Sider**: 20+
- **Datamodeller**: 8

### Teknologier og avhengigheter

- **Hovedavhengigheter**: 45+
- **DevDependencies**: 15+
- **Språk**: TypeScript (95%), JavaScript (5%)

### Ytelse

- **Lighthouse Score**: 85+ (estimert)
- **Bundle-størrelse**: ~2MB (optimaliserbar)
- **Lastetid**: <3s (utvikling)

---

## 🎯 Konklusjon

### Oppnådde mål

✅ Fungerende full-stack system
✅ Flere brukertyper
✅ Moderne og responsivt grensesnitt
✅ Robust database
✅ Sikker autentisering
✅ Analytisk dashboard

### Lærdommer

- **Full-stack utvikling** med Next.js
- **Kompleks tilstandshåndtering**
- **Relasjonsdatabasedesign**
- **Skalerbar komponentarkitektur**
- **Agile utviklingsmetodikk**

### Akademisk verdi

Dette prosjektet demonstrerer kompetanse innen:

- **Kravanalyse**
- **Systemdesign**
- **Teknisk implementering**
- **Prosjekthåndtering**
- **Problemløsning**

### Neste steg

1. Implementere høyprioritetsforbedringer
2. Legge til automatiserte tester
3. Optimalisere ytelse
4. Forberede for produksjon

---

**Utviklet av**: [Ditt navn]
**Periode**: [Utviklingsperiode]
**Institusjon**: [Universitets navn]
**Kurs**: [Kurs navn]
