import NextAuth from "next-auth";
import authConfig from "@/auth.config";
import authConfigMock from "@/auth.config.mock";
import { useMockData } from "@/lib/use-mock-data";

// Usar a configuração de autenticação mockada se USE_MOCK_DATA for true
const config = useMockData() ? authConfigMock : authConfig;
console.log("auth.ts - Using mock auth:", useMockData());

export const {
  handlers: { GET, POST },
  auth,
  signIn,
  signOut,
} = NextAuth({
  session: { strategy: "jwt" },
  ...config,
});
