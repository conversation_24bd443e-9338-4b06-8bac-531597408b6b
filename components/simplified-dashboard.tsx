"use client";

import {
  useState,
  Suspense,
  useRef,
  useCallback,
  useMemo,
  RefObject,
} from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Bar<PERSON>hart3,
  LineChart,
  PieChart,
  TrendingUp,
  Calendar,
  Users,
  Car,
  Clock,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import {
  LazyResponsive<PERSON>hart as Responsive<PERSON>hart,
  ResponsiveChartSkeleton,
} from "@/components/lazy-components";
import { useSwipe } from "@/hooks/use-swipe";

interface SimplifiedDashboardProps {
  stats: any;
  bookings: any[];
}

interface Tab {
  id: string;
  label: string;
}

export function SimplifiedDashboard({
  stats,
  bookings,
}: SimplifiedDashboardProps) {
  const [activeTab, setActiveTab] = useState<string>("overview");
  const containerRef = useRef<HTMLDivElement>(null);

  // Flytte tabs-array til useMemo for å forhindre nyskapelse ved hver rendering
  const tabs = useMemo<Tab[]>(
    () => [
      { id: "overview", label: "Oversikt" },
      { id: "bookings", label: "Bestillinger" },
      { id: "stats", label: "Statistikk" },
    ],
    []
  );

  // Oppdatere useCallback for å inkludere tabs i avhengigheter
  const goToNextTab = useCallback(() => {
    const currentIndex = tabs.findIndex((tab) => tab.id === activeTab);
    const nextIndex = (currentIndex + 1) % tabs.length;
    setActiveTab(tabs[nextIndex].id);
  }, [activeTab, tabs]);

  // Oppdatere useCallback for å inkludere tabs i avhengigheter
  const goToPrevTab = useCallback(() => {
    const currentIndex = tabs.findIndex((tab) => tab.id === activeTab);
    const prevIndex = (currentIndex - 1 + tabs.length) % tabs.length;
    setActiveTab(tabs[prevIndex].id);
  }, [activeTab, tabs]);

  useSwipe({
    ref: containerRef,
    onSwipeLeft: goToNextTab,
    onSwipeRight: goToPrevTab,
  });

  // Hente ut viktige data fra statistikken
  const totalBookings = stats.totalBookings || stats.bookings?.total || 0;
  const totalRevenue = stats.revenue?.total || 0;
  const activeDrivers = stats.activeDrivers || stats.drivers?.active || 0;
  const activeVehicles = stats.availableVehicles || stats.vehicles?.active || 0;

  // Forenklet data for grafene
  const bookingData = {
    labels: ["Jan", "Feb", "Mar", "Apr", "Mai", "Jun"],
    datasets: [
      {
        label: "Bestillinger",
        data: [65, 59, 80, 81, 56, 55],
        backgroundColor: "rgba(59, 130, 246, 0.5)",
      },
    ],
  };

  const statusData = {
    labels: ["Bekreftet", "Venter", "Fullført", "Kansellert"],
    datasets: [
      {
        label: "Status",
        data: [40, 20, 30, 10],
        backgroundColor: [
          "rgba(34, 197, 94, 0.5)",
          "rgba(59, 130, 246, 0.5)",
          "rgba(168, 85, 247, 0.5)",
          "rgba(239, 68, 68, 0.5)",
        ],
      },
    ],
  };

  return (
    <div className="space-y-3 xs:space-y-4" ref={containerRef}>
      <div className="grid grid-cols-2 gap-1.5 xs:gap-2 sm:gap-3">
        <Card className="bg-blue-50 dark:bg-blue-950/20 touch-manipulation hover:shadow-sm transition-shadow border-0 xs:border">
          <CardContent className="p-1.5 xs:p-2 sm:p-3">
            <div className="flex items-center gap-1.5 xs:gap-2">
              <div className="flex items-center justify-center bg-blue-100 dark:bg-blue-900/30 rounded-full p-1.5 xs:p-2">
                <Calendar className="h-3 w-3 xs:h-4 xs:w-4 text-blue-500" />
              </div>
              <div>
                <p className="text-[10px] xs:text-xs text-muted-foreground">
                  Bestillinger
                </p>
                <p className="text-base xs:text-lg font-bold">
                  {totalBookings}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-green-50 dark:bg-green-950/20 touch-manipulation hover:shadow-sm transition-shadow border-0 xs:border">
          <CardContent className="p-1.5 xs:p-2 sm:p-3">
            <div className="flex items-center gap-1.5 xs:gap-2">
              <div className="flex items-center justify-center bg-green-100 dark:bg-green-900/30 rounded-full p-1.5 xs:p-2">
                <TrendingUp className="h-3 w-3 xs:h-4 xs:w-4 text-green-500" />
              </div>
              <div className="truncate">
                <p className="text-[10px] xs:text-xs text-muted-foreground">
                  Inntekter
                </p>
                <p className="text-base xs:text-lg font-bold truncate">
                  {totalRevenue.toLocaleString()} kr
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-purple-50 dark:bg-purple-950/20 touch-manipulation hover:shadow-sm transition-shadow border-0 xs:border">
          <CardContent className="p-1.5 xs:p-2 sm:p-3">
            <div className="flex items-center gap-1.5 xs:gap-2">
              <div className="flex items-center justify-center bg-purple-100 dark:bg-purple-900/30 rounded-full p-1.5 xs:p-2">
                <Users className="h-3 w-3 xs:h-4 xs:w-4 text-purple-500" />
              </div>
              <div>
                <p className="text-[10px] xs:text-xs text-muted-foreground">
                  Sjåfører
                </p>
                <p className="text-base xs:text-lg font-bold">
                  {activeDrivers}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-amber-50 dark:bg-amber-950/20 touch-manipulation hover:shadow-sm transition-shadow border-0 xs:border">
          <CardContent className="p-1.5 xs:p-2 sm:p-3">
            <div className="flex items-center gap-1.5 xs:gap-2">
              <div className="flex items-center justify-center bg-amber-100 dark:bg-amber-900/30 rounded-full p-1.5 xs:p-2">
                <Car className="h-3 w-3 xs:h-4 xs:w-4 text-amber-500" />
              </div>
              <div>
                <p className="text-[10px] xs:text-xs text-muted-foreground">
                  Kjøretøy
                </p>
                <p className="text-base xs:text-lg font-bold">
                  {activeVehicles}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Faner for forenklede grafer - Forbedret for mobil */}
      <div className="relative">
        <div className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1 z-10 hidden xs:flex">
          <Button
            variant="ghost"
            size="icon"
            className="h-5 w-5 xs:h-6 xs:w-6 rounded-full bg-background/80 backdrop-blur-sm shadow-sm hover:bg-background"
            onClick={goToPrevTab}
          >
            <ChevronLeft className="h-3 w-3 xs:h-4 xs:w-4" />
            <span className="sr-only">Forrige</span>
          </Button>
        </div>

        <div className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-1 z-10 hidden xs:flex">
          <Button
            variant="ghost"
            size="icon"
            className="h-5 w-5 xs:h-6 xs:w-6 rounded-full bg-background/80 backdrop-blur-sm shadow-sm hover:bg-background"
            onClick={goToNextTab}
          >
            <ChevronRight className="h-3 w-3 xs:h-4 xs:w-4" />
            <span className="sr-only">Neste</span>
          </Button>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="w-full grid grid-cols-3 h-auto p-0.5 xs:p-1">
            <TabsTrigger
              value="overview"
              className="text-[10px] xs:text-xs py-1 xs:py-1.5 px-1 xs:px-2 rounded-sm xs:rounded focus-visible:ring-1 active:scale-95 transition-transform"
              onClick={() => setActiveTab("overview")}
            >
              <BarChart3 className="h-2.5 w-2.5 xs:h-3 xs:w-3 mr-0.5 xs:mr-1 flex-shrink-0" />
              <span className="truncate">Oversikt</span>
            </TabsTrigger>
            <TabsTrigger
              value="bookings"
              className="text-[10px] xs:text-xs py-1 xs:py-1.5 px-1 xs:px-2 rounded-sm xs:rounded focus-visible:ring-1 active:scale-95 transition-transform"
              onClick={() => setActiveTab("bookings")}
            >
              <LineChart className="h-2.5 w-2.5 xs:h-3 xs:w-3 mr-0.5 xs:mr-1 flex-shrink-0" />
              <span className="truncate">Bestillinger</span>
            </TabsTrigger>
            <TabsTrigger
              value="status"
              className="text-[10px] xs:text-xs py-1 xs:py-1.5 px-1 xs:px-2 rounded-sm xs:rounded focus-visible:ring-1 active:scale-95 transition-transform"
              onClick={() => setActiveTab("status")}
            >
              <PieChart className="h-2.5 w-2.5 xs:h-3 xs:w-3 mr-0.5 xs:mr-1 flex-shrink-0" />
              <span className="truncate">Status</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="mt-1 xs:mt-2">
            <Card className="border-0 xs:border shadow-sm">
              <CardHeader className="p-2 xs:p-3">
                <CardTitle className="text-xs xs:text-sm font-medium">
                  Oversikt
                </CardTitle>
              </CardHeader>
              <CardContent className="p-2 xs:p-3 pt-0">
                <Suspense
                  fallback={
                    <div className="h-[180px]">
                      <Skeleton className="h-full w-full" />
                    </div>
                  }
                >
                  <ResponsiveChart
                    type="bar"
                    data={bookingData}
                    height={180}
                    title="Oversikt"
                    description="Bestillinger per måned"
                    allowTypeChange={false}
                    allowDownload={false}
                    allowFullscreen={false}
                  />
                </Suspense>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="bookings" className="mt-1 xs:mt-2">
            <Card className="border-0 xs:border shadow-sm">
              <CardHeader className="p-2 xs:p-3">
                <CardTitle className="text-xs xs:text-sm font-medium">
                  Bestillinger over tid
                </CardTitle>
              </CardHeader>
              <CardContent className="p-2 xs:p-3 pt-0">
                <Suspense
                  fallback={
                    <div className="h-[180px]">
                      <Skeleton className="h-full w-full" />
                    </div>
                  }
                >
                  <ResponsiveChart
                    type="line"
                    data={bookingData}
                    height={180}
                    title="Bestillinger over tid"
                    description="Utvikling av bestillinger"
                    allowTypeChange={false}
                    allowDownload={false}
                    allowFullscreen={false}
                  />
                </Suspense>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="status" className="mt-1 xs:mt-2">
            <Card className="border-0 xs:border shadow-sm">
              <CardHeader className="p-2 xs:p-3">
                <CardTitle className="text-xs xs:text-sm font-medium">
                  Status fordeling
                </CardTitle>
              </CardHeader>
              <CardContent className="p-2 xs:p-3 pt-0">
                <Suspense
                  fallback={
                    <div className="h-[180px]">
                      <Skeleton className="h-full w-full" />
                    </div>
                  }
                >
                  <ResponsiveChart
                    type="doughnut"
                    data={statusData}
                    height={180}
                    title="Status fordeling"
                    description="Fordeling av bestillinger etter status"
                    allowTypeChange={false}
                    allowDownload={false}
                    allowFullscreen={false}
                  />
                </Suspense>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Sveipetips for mobil */}
      <div className="text-center text-xs text-muted-foreground mt-1 xs:hidden">
        <p className="flex items-center justify-center gap-1">
          <ChevronLeft className="h-3 w-3" />
          <span>Sveip for å navigere</span>
          <ChevronRight className="h-3 w-3" />
        </p>
      </div>

      {/* Knapp for å se fullstendig dashbord - Forbedret for mobil */}
      <div className="pt-1 xs:pt-2 sm:pt-3">
        <Button
          variant="outline"
          size="sm"
          className="w-full text-[10px] xs:text-xs sm:text-sm py-1 xs:py-1.5 sm:py-2 h-auto rounded-sm xs:rounded flex items-center justify-center gap-1 xs:gap-1.5 hover:bg-primary/5 active:scale-[0.98] transition-transform border-0 xs:border"
          onClick={() => {
            window.localStorage.setItem("preferFullDashboard", "true");
            // Laste inn siden på nytt for å bruke endringen
            window.location.reload();
          }}
        >
          <span className="inline-block">Vis fullstendig dashboard</span>
          <TrendingUp className="h-2.5 w-2.5 xs:h-3 xs:w-3 sm:h-4 sm:w-4 opacity-70" />
        </Button>
      </div>
    </div>
  );
}
