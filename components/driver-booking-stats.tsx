"use client";

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import {
  Clock,
  CheckCircle,
  AlertCircle,
  Calendar,
  TrendingUp,
} from "lucide-react";

interface BookingStatsProps {
  stats: {
    status: string;
    _count: number;
  }[];
  totalBookings: number;
}

export function DriverBookingStats({
  stats,
  totalBookings,
}: BookingStatsProps) {
  // Finne antall per status
  const getCountByStatus = (status: string) => {
    const found = stats.find((s) => s.status === status);
    return found ? found._count : 0;
  };

  const pendingCount = getCountByStatus("PENDING");
  const confirmedCount = getCountByStatus("CONFIRMED");
  const inProgressCount = getCountByStatus("IN_PROGRESS");
  const completedCount = getCountByStatus("COMPLETED");
  const cancelledCount = getCountByStatus("CANCELLED");

  // <PERSON><PERSON>gne statistikk
  const completionRate =
    totalBookings > 0 ? Math.round((completedCount / totalBookings) * 100) : 0;

  return (
    <div className="grid gap-4 grid-cols-2 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xs sm:text-sm font-medium">
            Totalt antall oppdrag
          </CardTitle>
          <Calendar className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-xl sm:text-2xl font-bold">{totalBookings}</div>
          <p className="text-xs text-muted-foreground">
            {completedCount} fullførte oppdrag
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xs sm:text-sm font-medium">
            Aktive oppdrag
          </CardTitle>
          <Clock className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-xl sm:text-2xl font-bold">
            {confirmedCount + inProgressCount}
          </div>
          <p className="text-xs text-muted-foreground">
            {inProgressCount} pågående, {confirmedCount} bekreftet
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xs sm:text-sm font-medium">
            Fullføringsrate
          </CardTitle>
          <TrendingUp className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-xl sm:text-2xl font-bold">{completionRate}%</div>
          <p className="text-xs text-muted-foreground">
            Av alle tildelte oppdrag
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xs sm:text-sm font-medium">
            Ventende oppdrag
          </CardTitle>
          <AlertCircle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-xl sm:text-2xl font-bold">{pendingCount}</div>
          <p className="text-xs text-muted-foreground">
            {cancelledCount} kansellerte oppdrag
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
