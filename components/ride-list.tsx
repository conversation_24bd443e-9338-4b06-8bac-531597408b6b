"use client";

import { useState, useEffect } from "react";
import { format, parseISO } from "date-fns";
import { nb } from "date-fns/locale";
import { useSession } from "next-auth/react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Car, Calendar, RotateCcw, Bus, Truck } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { ReceiptOptionsModal } from "@/components/receipt-options-modal";
import { generateReceiptPDF } from "@/lib/generate-receipt";
import { useToast } from "@/components/ui/use-toast";
import { DriverProfileModal } from "@/components/driver-profile-modal";
import { SyncDriverDataInRideDetails } from "@/components/sync-driver-data-in-ride-details";
import { EnsureDriverDataUpdated } from "@/components/ensure-driver-data-updated";

type BookingStatus =
  | "PENDING"
  | "CONFIRMED"
  | "IN_PROGRESS"
  | "COMPLETED"
  | "CANCELLED";

interface Booking {
  id: string;
  status: BookingStatus;
  pickupLocation: string;
  dropoffLocation: string;
  scheduledTime: string | Date;
  vehicle: {
    type?: string;
    plateNumber?: string;
    model?: string;
  };
  customer?: {
    name: string;
  };
  driver?: {
    name: string;
    email?: string;
    phone?: string | null;
    image?: string;
  } | null;
  payment?: {
    amount: number;
    method: string;
    status: string;
  } | null;
}

interface RideListProps {
  bookings: Booking[];
}

export function RideList({ bookings }: RideListProps) {
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [filter, setFilter] = useState<string>("all");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isReceiptModalOpen, setIsReceiptModalOpen] = useState(false);

  // Filtrar apenas corridas completadas e canceladas
  const filteredBookings = bookings.filter(
    (booking) =>
      booking.status === "COMPLETED" || booking.status === "CANCELLED"
  );

  // Ordenar as corridas da mais recente para a mais antiga
  const sortedBookings = [...filteredBookings].sort((a, b) => {
    return (
      new Date(b.scheduledTime).getTime() - new Date(a.scheduledTime).getTime()
    );
  });

  // Agrupar as corridas por mês
  const groupRidesByMonth = (rides: Booking[]) => {
    const groups: Record<string, Booking[]> = {};

    rides.forEach((ride) => {
      const date = new Date(ride.scheduledTime);
      // Capitalize o primeiro caractere do mês
      const monthYear = format(date, "MMMM yyyy", { locale: nb }).replace(
        /^\w/,
        (c) => c.toUpperCase()
      );

      if (!groups[monthYear]) {
        groups[monthYear] = [];
      }

      groups[monthYear].push(ride);
    });

    // Ordenar as corridas dentro de cada mês (mais recentes primeiro)
    Object.keys(groups).forEach((month) => {
      groups[month].sort((a, b) => {
        return (
          new Date(b.scheduledTime).getTime() -
          new Date(a.scheduledTime).getTime()
        );
      });
    });

    // Ordenar os meses do mais recente para o mais antigo
    const sortedGroups: Record<string, Booking[]> = {};

    // Criar um array de pares [mês, corridas] para ordenação
    const monthEntries = Object.entries(groups);

    // Ordenar os meses pelo timestamp da primeira corrida de cada mês (mais recente primeiro)
    monthEntries.sort((a, b) => {
      const aDate = new Date(a[1][0].scheduledTime).getTime();
      const bDate = new Date(b[1][0].scheduledTime).getTime();
      return bDate - aDate;
    });

    // Reconstruir o objeto ordenado
    monthEntries.forEach(([month, rides]) => {
      sortedGroups[month] = rides;
    });

    return sortedGroups;
  };

  const ridesByMonth = groupRidesByMonth(sortedBookings);

  // Filtrar as corridas com base na seleção
  const filterRides = (rides: Record<string, Booking[]>) => {
    if (filter === "all") return rides;

    const filtered: Record<string, Booking[]> = {};

    Object.keys(rides).forEach((month) => {
      filtered[month] = rides[month].filter((ride) => {
        if (filter === "completed") return ride.status === "COMPLETED";
        if (filter === "cancelled") return ride.status === "CANCELLED";
        return true;
      });

      // Remover meses vazios
      if (filtered[month].length === 0) {
        delete filtered[month];
      }
    });

    return filtered;
  };

  const filteredRides = filterRides(ridesByMonth);

  // Renderizar um item de corrida
  const renderRideItem = (ride: Booking) => {
    const isCompleted = ride.status === "COMPLETED";
    const isCancelled = ride.status === "CANCELLED";

    return (
      <Dialog
        key={`dialog-${ride.id}`}
        open={isDialogOpen && selectedBooking?.id === ride.id}
        onOpenChange={(open) => {
          setIsDialogOpen(open);
          if (!open) setSelectedBooking(null);
        }}
      >
        <DialogTrigger asChild>
          <div
            key={ride.id}
            className="border-b border-gray-200 dark:border-gray-700 py-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
            onClick={() => {
              setSelectedBooking(ride);
              setIsDialogOpen(true);

              // Forçar a sincronização dos dados do driver quando o usuário clicar em uma corrida
              if (typeof window !== "undefined" && window.forceSyncDriverData) {
                // @ts-ignore - Acessando função global
                window.forceSyncDriverData();
                console.log("RideItem: Forced driver data sync on click");
              }
            }}
          >
            <div className="flex items-center px-3">
              <div className="mr-3 flex items-center justify-center">
                {isCancelled ? (
                  <Badge
                    variant="destructive"
                    className="h-5 w-5 flex items-center justify-center p-0"
                  >
                    <span className="text-xs">X</span>
                  </Badge>
                ) : (
                  <>
                    {ride.vehicle?.type === "MINIVAN" ? (
                      <Truck className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                    ) : ride.vehicle?.type === "BUS" ||
                      ride.vehicle?.type === "MINIBUS" ? (
                      <Bus className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                    ) : (
                      <Car className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                    )}
                  </>
                )}
              </div>
              <div className="flex-1">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {format(new Date(ride.scheduledTime), "d MMMM", {
                        locale: nb,
                      }).replace(/^\w/, (c) => c.toUpperCase())}{" "}
                      ·{format(new Date(ride.scheduledTime), " HH:mm")}
                      {isCancelled && " · Avbrutt"}
                    </p>
                    <p className="mt-1 font-normal dark:text-white">
                      {ride.dropoffLocation}
                    </p>
                    <p className="text-sm font-bold text-gray-700 dark:text-gray-300">
                      {isCancelled
                        ? "0 NOK"
                        : ride.payment
                        ? `${(ride.payment.amount * 1.25).toFixed(2)} NOK`
                        : ""}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </DialogTrigger>
        <DialogContent
          className={`p-0 overflow-hidden
            ${
              selectedBooking?.id === ride.id && isReceiptModalOpen
                ? "!border-opacity-20 !border-gray-400/20"
                : ""
            }`}
        >
          <BookingDetails
            booking={selectedBooking}
            onClose={() => setIsDialogOpen(false)}
            onReceiptModalOpen={(open) => setIsReceiptModalOpen(open)}
            isReceiptModalOpen={isReceiptModalOpen}
          />
        </DialogContent>
      </Dialog>
    );
  };

  return (
    <div className="space-y-4">
      {/* Incluir os componentes para garantir que os dados do driver sejam sincronizados */}
      <SyncDriverDataInRideDetails driverId="driver1" />

      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold dark:text-white">Turer</h1>
        <Select value={filter} onValueChange={setFilter}>
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="Alle turer" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Alle turer</SelectItem>
            <SelectItem value="completed">Fullførte</SelectItem>
            <SelectItem value="cancelled">Avbrutt</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="mt-4">
        {Object.keys(filteredRides).length === 0 ? (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            Ingen turer funnet
          </div>
        ) : (
          Object.entries(filteredRides).map(([month, rides]) => (
            <div key={month} className="mb-6">
              <h2 className="text-lg font-semibold mb-2 dark:text-white">
                {month}
              </h2>
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                {rides.map(renderRideItem)}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}

function BookingDetails({
  booking,
  onClose,
  onReceiptModalOpen,
  isReceiptModalOpen,
}: {
  booking: Booking | null;
  onClose: () => void;
  onReceiptModalOpen: (open: boolean) => void;
  isReceiptModalOpen: boolean;
}) {
  const { data: session } = useSession();
  const { toast } = useToast();
  const [isDriverProfileOpen, setIsDriverProfileOpen] = useState(false);

  // Forçar a sincronização dos dados do driver quando o componente for montado
  useEffect(() => {
    if (booking?.driver) {
      console.log("BookingDetails: Forcing driver data sync...");

      // Importar as funções necessárias
      import("@/lib/initialize-mock-data").then(
        ({ updateMockDataFromLocalStorage }) => {
          // Atualizar os dados mockados com os dados do localStorage
          updateMockDataFromLocalStorage();
          console.log("BookingDetails: Updated mock data from localStorage");

          // Forçar a sincronização dos dados do driver
          import("@/lib/sync-driver-data").then(({ forceSyncDriverData }) => {
            forceSyncDriverData();
            console.log("BookingDetails: Forced driver data sync");
          });
        }
      );
    }
  }, [booking]);

  if (!booking) return null;

  const formattedDate = format(
    new Date(booking.scheduledTime),
    "EEE, MMM d, yyyy",
    { locale: nb }
  )
    .replace(/^\w/, (c) => c.toUpperCase())
    .replace(
      /,\s([a-z])/,
      (_, firstLetter) => `, ${firstLetter.toUpperCase()}`
    );

  const formattedPickupTime = format(new Date(booking.scheduledTime), "HH:mm");

  // Calcular o tempo estimado de chegada (25 minutos depois da partida como exemplo)
  const dropoffTime = new Date(booking.scheduledTime);
  dropoffTime.setMinutes(dropoffTime.getMinutes() + 25);
  const formattedDropoffTime = format(dropoffTime, "HH:mm");

  // Último 4 dígitos do cartão do cliente de exemplo
  const lastFourDigits = "4242";
  const cardBrand = "Visa";

  // Função para gerar e baixar o recibo em PDF
  const handleDownloadReceipt = async () => {
    try {
      const userName = session?.user?.name || "Kunde";
      const pdfDataUri = await generateReceiptPDF(booking, userName);

      // Criar um link temporário para download
      const link = document.createElement("a");
      link.href = pdfDataUri;
      link.download = `kvittering-${booking.id}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      onReceiptModalOpen(false);

      toast({
        title: "Kvittering lastet ned",
        description: "Kvitteringen er lastet ned til din enhet.",
        duration: 3000,
      });
    } catch (error) {
      console.error("Erro ao gerar PDF:", error);
      toast({
        title: "Feil",
        description: "Det oppstod en feil under nedlasting av kvitteringen.",
        variant: "destructive",
        duration: 3000,
      });
      onReceiptModalOpen(false);
    }
  };

  // Função para enviar o recibo por e-mail
  const handleSendReceiptByEmail = async () => {
    try {
      onReceiptModalOpen(false);

      // Simulação de envio de e-mail
      // Em uma implementação real, aqui você chamaria uma API para enviar o e-mail

      toast({
        title: "Kvittering sendt",
        description: "Kvitteringen er sendt til din e-postadresse.",
        duration: 3000,
      });
    } catch (error) {
      console.error("Erro ao enviar e-mail:", error);
      toast({
        title: "Feil",
        description: "Det oppstod en feil under sending av kvitteringen.",
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  return (
    <div className="flex flex-col h-full max-sm:min-h-screen max-sm:w-full">
      {/* Garantir que os dados do motorista estejam atualizados para esta corrida específica */}
      <EnsureDriverDataUpdated bookingId={booking.id} driverId="driver1" />

      {/* Verificar os dados do motorista */}
      {console.log(
        `BookingDetails - Driver: ${
          booking.driver?.name
        }, Image: ${booking.driver?.image?.substring(0, 30)}...`
      )}

      {/* Cabeçalho com botão de voltar */}
      <div className="p-4 border-b dark:border-gray-700">
        {/* Botão de voltar */}
        <div className="mb-2">
          <button
            onClick={onClose}
            className="text-gray-500 dark:text-gray-400"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M19 12H5M12 19l-7-7 7-7" />
            </svg>
          </button>
        </div>

        {/* Informações da corrida com foto do motorista */}
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-xl font-semibold dark:text-white">
              {booking.status === "CANCELLED"
                ? "Turen avbestilt"
                : `Kjør med ${booking.driver?.name || "Petre"}`}
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {formattedDate}
            </p>
          </div>

          {/* Foto do motorista - apenas se não for cancelada */}
          {booking.status !== "CANCELLED" && (
            <button
              onClick={() => setIsDriverProfileOpen(true)}
              className="w-10 h-10 rounded-full bg-gray-200 dark:bg-gray-700 overflow-hidden flex items-center justify-center ml-2 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              aria-label="Ver perfil do motorista"
            >
              {booking.driver?.image ? (
                <img
                  src={booking.driver.image}
                  alt={booking.driver.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="w-6 h-6 text-gray-500 dark:text-gray-400"
                >
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                  <circle cx="12" cy="7" r="4"></circle>
                </svg>
              )}
            </button>
          )}
        </div>
      </div>

      {/* Informações da viagem */}
      <div className="p-4 space-y-6">
        {/* Locais e horários */}
        <div className="relative pl-6">
          {/* Linha vertical conectando os pontos */}
          <div className="absolute left-[9px] top-[24px] bottom-[24px] w-0.5 bg-gray-200 dark:bg-gray-700"></div>

          {/* Ponto de partida */}
          <div className="flex items-start mb-6 relative">
            <div className="absolute left-[-6px] top-1 w-3 h-3 rounded-full bg-green-500"></div>
            <div className="ml-6">
              <p className="font-medium dark:text-white">
                {booking.pickupLocation}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {formattedPickupTime}
              </p>
            </div>
          </div>

          {/* Ponto de chegada */}
          <div className="flex items-start relative">
            <div className="absolute left-[-6px] top-1 w-3 h-3 rounded-full bg-blue-500"></div>
            <div className="ml-6">
              <p className="font-medium dark:text-white">
                {booking.dropoffLocation}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {formattedDropoffTime}
              </p>
            </div>
          </div>
        </div>

        {/* Botões de ação */}
        <div className="space-y-3">
          <button className="w-full py-3 px-4 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 rounded-lg text-center font-medium transition-colors">
            Få hjelp med en tur
          </button>
          <button className="w-full py-3 px-4 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 rounded-lg text-center font-medium transition-colors">
            Ombestill
          </button>
        </div>

        {booking.status !== "CANCELLED" && (
          <p className="text-sm text-gray-500 dark:text-gray-400 text-center">
            Ytterligere turdetaljer finner du i e-postkvitteringen din
          </p>
        )}
      </div>

      {/* Seção de pagamento */}
      <div className="mt-auto">
        <div className="p-4 border-t dark:border-gray-700">
          <h3 className="text-lg font-semibold mb-4 dark:text-white">
            Betaling
          </h3>

          {/* Detalhes do pagamento */}
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <span className="text-gray-700 dark:text-gray-300">
                  {booking.status === "CANCELLED"
                    ? "Avbestillingsgebyr"
                    : "Pris • VikenTours"}
                </span>
              </div>
              <span className="font-medium dark:text-white">
                {booking.status === "CANCELLED"
                  ? "0 NOK"
                  : booking.payment
                  ? `${(booking.payment.amount * 1.25).toFixed(2)} NOK`
                  : "0 NOK"}
              </span>
            </div>

            <div className="flex justify-between items-center">
              <span className="font-semibold dark:text-white">Totalt</span>
              <span className="font-semibold dark:text-white">
                {booking.status === "CANCELLED"
                  ? "0 NOK"
                  : booking.payment
                  ? `${(booking.payment.amount * 1.25).toFixed(2)} NOK`
                  : "0 NOK"}
              </span>
            </div>

            {booking.status !== "CANCELLED" && (
              <div className="flex justify-between items-center py-2">
                <div className="flex items-center">
                  <span className="inline-flex items-center justify-center w-8 h-5 bg-blue-900 rounded mr-2 text-white text-xs">
                    {cardBrand.toUpperCase()}
                  </span>
                  <span className="text-gray-700 dark:text-gray-300">
                    •••• {lastFourDigits}
                  </span>
                </div>
                <span className="font-medium dark:text-white">
                  {booking.payment
                    ? `${(booking.payment.amount * 1.25).toFixed(2)} NOK`
                    : "0 NOK"}
                </span>
              </div>
            )}
          </div>

          {/* Botão de recibo - apenas para viagens não canceladas */}
          {booking.status !== "CANCELLED" && (
            <button
              className="w-full mt-4 py-3 px-4 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 rounded-lg text-center font-medium transition-colors flex items-center justify-center"
              onClick={() => onReceiptModalOpen(true)}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="mr-2"
              >
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                <polyline points="7 10 12 15 17 10"></polyline>
                <line x1="12" y1="15" x2="12" y2="3"></line>
              </svg>
              Få kvittering
            </button>
          )}
        </div>
      </div>

      {/* Modal de opções de recibo - z-index maior para ficar sobre o modal de detalhes */}
      {isReceiptModalOpen && (
        <>
          {/* Overlay semi-transparente que escurece o modal de detalhes da corrida */}
          <div
            className="fixed inset-0 bg-black/30 z-[90] w-screen h-screen"
            aria-hidden="true"
            onClick={() => onReceiptModalOpen(false)}
          />
        </>
      )}
      <div className="relative z-[100]">
        <ReceiptOptionsModal
          isOpen={isReceiptModalOpen}
          onClose={() => onReceiptModalOpen(false)}
          onDownload={handleDownloadReceipt}
          onSendEmail={handleSendReceiptByEmail}
        />
      </div>

      {/* Modal de perfil do motorista */}
      <DriverProfileModal
        isOpen={isDriverProfileOpen}
        onClose={() => setIsDriverProfileOpen(false)}
        driver={{
          name: booking.driver?.name || "Motorista",
          licensePlate: booking.vehicle?.plateNumber || "B117NXD",
          carModel: booking.vehicle?.model || "Hyundai Elantra",
          phone: booking.driver?.phone || "+47 123 45 678",
          image: booking.driver?.image,
        }}
      />
    </div>
  );
}
