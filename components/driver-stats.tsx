"use client";

import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { CheckCircle, Clock, AlertCircle } from "lucide-react";

interface StatsProps {
  stats: {
    status: string;
    _count: number;
  }[];
}

export function DriverStats({ stats }: StatsProps) {
  const getStatusCount = (status: string) => {
    const found = stats.find((s) => s.status === status);
    return found ? found._count : 0;
  };

  const items = [
    {
      title: "Aktive turer",
      value: getStatusCount("IN_PROGRESS"),
      icon: Clock,
      description: "Turer under gjennomføring",
    },
    {
      title: "Bekreftet",
      value: getStatusCount("CONFIRMED"),
      icon: CheckCircle,
      description: "Kommende turer",
    },
    {
      title: "Fullførte turer",
      value: getStatusCount("COMPLETED"),
      icon: CheckCircle,
      description: "Totalt fullførte turer",
    },
    {
      title: "Kansellerte",
      value: getStatusCount("CANCELLED"),
      icon: AlertCircle,
      description: "Kansellerte turer",
    },
  ];

  return (
    <div className="grid gap-4 grid-cols-2 md:grid-cols-2 lg:grid-cols-4">
      {items.map((item) => (
        <Card key={item.title}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{item.title}</CardTitle>
            <item.icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{item.value}</div>
            <p className="text-xs text-muted-foreground">{item.description}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
