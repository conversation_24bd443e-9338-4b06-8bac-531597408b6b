"use client";

import { useEffect } from "react";
import { syncDriverData } from "@/lib/sync-driver-data";
import { updateMockDataFromLocalStorage } from "@/lib/initialize-mock-data";

export function SyncDriverData() {
  useEffect(() => {
    // Atualizar os dados mockados com os dados do localStorage
    updateMockDataFromLocalStorage();
    console.log(
      "Updated mock data from localStorage in SyncDriverData component"
    );

    // Sincronizar os dados do driver quando o componente for montado
    syncDriverData();
    console.log("Synced driver data in SyncDriverData component");

    // Adicionar um listener para sincronizar os dados quando o localStorage for alterado
    const handleStorageChange = (e: StorageEvent | Event) => {
      // Se for um StorageEvent, verificar a chave
      if (e instanceof StorageEvent) {
        if (
          e.key &&
          (e.key.startsWith("driver") ||
            e.key === "driverProfile" ||
            e.key === "driverProfileImage")
        ) {
          console.log(
            "Storage changed via StorageEvent, syncing driver data..."
          );
          updateMockDataFromLocalStorage();
          syncDriverData();
        }
      } else {
        // Se for um Event normal (disparado manualmente), sincronizar de qualquer forma
        console.log("Storage changed via custom event, syncing driver data...");
        updateMockDataFromLocalStorage();
        syncDriverData();
      }
    };

    // Adicionar o listener para eventos de storage
    window.addEventListener("storage", handleStorageChange);

    // Remover o listener quando o componente for desmontado
    return () => {
      window.removeEventListener("storage", handleStorageChange);
    };
  }, []);

  // Este componente não renderiza nada
  return null;
}
