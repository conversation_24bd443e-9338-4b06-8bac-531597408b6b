"use client";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Users, Car, Calendar, Clock } from "lucide-react";

interface StatsProps {
  stats: {
    totalBookings: number;
    activeDrivers: number;
    availableVehicles: number;
    pendingBookings: number;
  };
}

export function AdminStats({ stats }: StatsProps) {
  const items = [
    {
      title: "Totale bestillinger",
      value: stats.totalBookings,
      icon: Calendar,
      description: "Alle bestillinger i systemet",
      color: "bg-blue-100 dark:bg-blue-900/20",
      iconColor: "text-blue-600 dark:text-blue-400",
    },
    {
      title: "Aktive sjåfører",
      value: stats.activeDrivers,
      icon: Users,
      description: "Registrerte sjåfører",
      color: "bg-green-100 dark:bg-green-900/20",
      iconColor: "text-green-600 dark:text-green-400",
    },
    {
      title: "Tilgjenge<PERSON><PERSON> kjøretøy",
      value: stats.availableVehicles,
      icon: Car,
      description: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> klare for oppdrag",
      color: "bg-purple-100 dark:bg-purple-900/20",
      iconColor: "text-purple-600 dark:text-purple-400",
    },
    {
      title: "Ventende bestillinger",
      value: stats.pendingBookings,
      icon: Clock,
      description: "Bestillinger som venter på godkjenning",
      color: "bg-amber-100 dark:bg-amber-900/20",
      iconColor: "text-amber-600 dark:text-amber-400",
    },
  ];

  return (
    <div className="grid gap-4 grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 w-full">
      {items.map((item) => (
        <Card
          key={item.title}
          className="overflow-hidden border-none shadow-md hover:shadow-lg transition-shadow duration-200"
        >
          <div className={`${item.color} px-3 sm:px-6 py-3 sm:py-4`}>
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-xs sm:text-sm">{item.title}</h3>
              <div className={`rounded-full p-1.5 sm:p-2 ${item.color}`}>
                <item.icon
                  className={`h-4 w-4 sm:h-5 sm:w-5 ${item.iconColor}`}
                />
              </div>
            </div>
            <div className="mt-2 flex items-baseline">
              <div className="text-xl sm:text-3xl font-bold">{item.value}</div>
            </div>
          </div>
          <CardContent className="pt-3 sm:pt-4 pb-2">
            <p className="text-xs text-muted-foreground">{item.description}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
