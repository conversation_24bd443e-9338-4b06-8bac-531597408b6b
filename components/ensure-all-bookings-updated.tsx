"use client";

import { useEffect } from "react";
import { updateDriverInfoInBookings } from "@/lib/mock-data";
import { forceSyncDriverData } from "@/lib/sync-driver-data";

// Variável para controlar se o componente já foi montado
let componentMounted = false;

/**
 * Componente para garantir que todas as corridas tenham os dados do motorista atualizados
 * quando a página de corridas é carregada.
 *
 * Este componente é usado na página de listagem de corridas para garantir
 * que todas as corridas tenham as informações do motorista sincronizadas.
 */
export function EnsureAllBookingsUpdated() {
  console.log("EnsureAllBookingsUpdated: Component rendering");

  useEffect(() => {
    console.log("EnsureAllBookingsUpdated: useEffect triggered");

    if (componentMounted) {
      console.log(
        "EnsureAllBookingsUpdated: Component already mounted, skipping initialization"
      );
      return;
    }

    componentMounted = true;
    console.log(
      "EnsureAllBookingsUpdated: Ensuring all bookings have updated driver data"
    );
    console.log("EnsureAllBookingsUpdated: Component mounted");

    // Função para atualizar os dados do driver em todas as corridas
    const updateAllBookings = () => {
      // Obter os dados do driver do localStorage
      if (typeof window !== "undefined") {
        const driverProfileStr = localStorage.getItem("driverProfile");
        if (!driverProfileStr) {
          console.log(
            "EnsureAllBookingsUpdated: No driver profile found in localStorage"
          );
          return;
        }

        try {
          // Obter o driver profile completo
          const driverProfile = JSON.parse(driverProfileStr);

          // Construir o objeto de dados do driver
          const driverData = {
            name: driverProfile?.name || "",
            phone: driverProfile?.phone || "",
            image: driverProfile?.image || "",
            driverProfile: driverProfile?.driverProfile || {},
          };

          console.log(
            `EnsureAllBookingsUpdated: Driver data from localStorage:`,
            {
              name: driverData.name,
              phone: driverData.phone,
              image: driverData.image
                ? driverData.image.substring(0, 30) + "..."
                : "None",
            }
          );

          // Atualizar as informações do motorista em todas as corridas
          updateDriverInfoInBookings("driver1", driverData, true);
          console.log(
            "EnsureAllBookingsUpdated: Updated driver info in all bookings"
          );

          // Forçar a sincronização dos dados do driver
          forceSyncDriverData();
          console.log("EnsureAllBookingsUpdated: Forced driver data sync");

          // Disparar um evento de storage para notificar outros componentes
          window.dispatchEvent(new Event("storage"));
        } catch (e) {
          console.error(
            "EnsureAllBookingsUpdated: Error updating driver info:",
            e
          );
        }
      }
    };

    // Executar a atualização imediatamente
    updateAllBookings();

    // Configurar um intervalo para verificar e atualizar periodicamente
    // Isso garante que os dados sejam atualizados mesmo se o localStorage mudar
    const intervalId = setInterval(updateAllBookings, 5000);

    // Limpar o intervalo quando o componente for desmontado
    return () => clearInterval(intervalId);
  }, []);

  // Este componente não renderiza nada
  return null;
}
