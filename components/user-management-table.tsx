"use client";

import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { MoreHorizontal, Search, Plus, UserPlus, X } from "lucide-react";
import { format } from "date-fns";

export interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  createdAt: Date;
  // Campo active removido pois não existe no modelo User
}

interface UserManagementTableProps {
  users: User[];
  onCreateUser: (
    user: Omit<User, "id" | "createdAt"> & { password: string }
  ) => Promise<any>;
  onUpdateUser: (id: string, data: Partial<User>) => Promise<any>;
  onDeleteUser: (id: string) => Promise<any>;
}

export function UserManagementTable({
  users,
  onCreateUser,
  onUpdateUser,
  onDeleteUser,
}: UserManagementTableProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [roleFilter, setRoleFilter] = useState<string | null>("all");
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [newUser, setNewUser] = useState({
    name: "",
    email: "",
    role: "USER",
    password: "",
    // Campo active removido pois não existe no modelo User
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Filtrere brukere basert på søkeord og rollefilter
  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      searchQuery === "" ||
      user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesRole =
      roleFilter === null || roleFilter === "all" || user.role === roleFilter;

    return matchesSearch && matchesRole;
  });

  // Håndtere opprettelse av ny bruker
  const handleCreateUser = async () => {
    try {
      setIsSubmitting(true);
      const result = await onCreateUser({
        name: newUser.name,
        email: newUser.email,
        role: newUser.role,
        password: newUser.password,
        // Campo active removido pois não existe no modelo User
      });

      if (result.success) {
        console.log("User created successfully");
        setShowCreateDialog(false);
        setNewUser({
          name: "",
          email: "",
          role: "USER",
          password: "",
          // Campo active removido pois não existe no modelo User
        });
      } else {
        console.error("Failed to create user:", result.error);
        alert(`Feil ved opprettelse av bruker: ${result.error}`);
      }
    } catch (error) {
      console.error("Exception when creating user:", error);
      alert(`En feil oppstod: ${error instanceof Error ? error.message : 'Ukjent feil'}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Håndtere oppdatering av bruker
  const handleUpdateUser = async () => {
    if (!currentUser) return;

    try {
      setIsSubmitting(true);
      const result = await onUpdateUser(currentUser.id, {
        name: currentUser.name,
        email: currentUser.email,
        role: currentUser.role,
        // Campo active removido pois não existe no modelo User
      });

      if (result.success) {
        console.log("User updated successfully");
        setShowEditDialog(false);
      } else {
        console.error("Failed to update user:", result.error);
        alert(`Feil ved oppdatering av bruker: ${result.error}`);
      }
    } catch (error) {
      console.error("Exception when updating user:", error);
      alert(`En feil oppstod: ${error instanceof Error ? error.message : 'Ukjent feil'}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Håndtere sletting av bruker
  const handleDeleteUser = async () => {
    if (!currentUser) return;

    try {
      setIsSubmitting(true);
      const result = await onDeleteUser(currentUser.id);

      if (result.success) {
        console.log("User deleted successfully");
        setShowDeleteDialog(false);
      } else {
        console.error("Failed to delete user:", result.error);
        alert(`Feil ved sletting av bruker: ${result.error}`);
      }
    } catch (error) {
      console.error("Exception when deleting user:", error);
      alert(`En feil oppstod: ${error instanceof Error ? error.message : 'Ukjent feil'}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Hente merkevariant basert på rolle
  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case "ADMIN":
        return "destructive";
      case "DRIVER":
        return "default";
      case "CUSTOMER":
        return "secondary";
      default:
        return "outline";
    }
  };

  // Oversette rolle til norsk
  const translateRole = (role: string) => {
    switch (role) {
      case "ADMIN":
        return "Administrator";
      case "DRIVER":
        return "Sjåfør";
      case "CUSTOMER":
        return "Kunde";
      case "USER":
        return "Bruker";
      default:
        return role;
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <div className="flex items-center gap-2 w-full sm:w-auto">
          <div className="relative w-full sm:w-[300px]">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Søk etter brukere..."
              className="pl-8 w-full"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            {searchQuery && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0 h-9 w-9"
                onClick={() => setSearchQuery("")}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
          <Select
            value={roleFilter || "all"}
            onValueChange={(value) => setRoleFilter(value || null)}
          >
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Alle roller" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Alle roller</SelectItem>
              <SelectItem value="ADMIN">Administrator</SelectItem>
              <SelectItem value="DRIVER">Sjåfør</SelectItem>
              <SelectItem value="CUSTOMER">Kunde</SelectItem>
              <SelectItem value="USER">Bruker</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <Button
          onClick={() => setShowCreateDialog(true)}
          className="w-full sm:w-auto"
        >
          <UserPlus className="mr-2 h-4 w-4" />
          Legg til bruker
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Navn</TableHead>
              <TableHead>E-post</TableHead>
              <TableHead>Rolle</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Opprettet</TableHead>
              <TableHead className="w-[80px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredUsers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8">
                  {searchQuery || roleFilter ? (
                    <div className="flex flex-col items-center justify-center text-muted-foreground">
                      <Search className="h-8 w-8 mb-2" />
                      <p>Ingen brukere funnet med disse filterene</p>
                      <Button
                        variant="link"
                        onClick={() => {
                          setSearchQuery("");
                          setRoleFilter(null);
                        }}
                      >
                        Tilbakestill filtre
                      </Button>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center text-muted-foreground">
                      <UserPlus className="h-8 w-8 mb-2" />
                      <p>Ingen brukere funnet</p>
                      <Button
                        variant="link"
                        onClick={() => setShowCreateDialog(true)}
                      >
                        Legg til din første bruker
                      </Button>
                    </div>
                  )}
                </TableCell>
              </TableRow>
            ) : (
              filteredUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell className="font-medium">{user.name}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>
                    <Badge variant={getRoleBadgeVariant(user.role)}>
                      {translateRole(user.role)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant="outline"
                    >
                      Aktiv
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {format(new Date(user.createdAt), "dd.MM.yyyy")}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Åpne meny</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Handlinger</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => {
                            setCurrentUser(user);
                            setShowEditDialog(true);
                          }}
                        >
                          Rediger
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => {
                            setCurrentUser(user);
                            setShowDeleteDialog(true);
                          }}
                          className="text-destructive focus:text-destructive"
                        >
                          Slett
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Dialog for å opprette bruker */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Legg til ny bruker</DialogTitle>
            <DialogDescription>
              Fyll ut informasjonen nedenfor for å opprette en ny bruker.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Navn
              </Label>
              <Input
                id="name"
                value={newUser.name}
                onChange={(e) =>
                  setNewUser({ ...newUser, name: e.target.value })
                }
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="email" className="text-right">
                E-post
              </Label>
              <Input
                id="email"
                type="email"
                value={newUser.email}
                onChange={(e) =>
                  setNewUser({ ...newUser, email: e.target.value })
                }
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="password" className="text-right">
                Passord
              </Label>
              <Input
                id="password"
                type="password"
                value={newUser.password}
                onChange={(e) =>
                  setNewUser({ ...newUser, password: e.target.value })
                }
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="role" className="text-right">
                Rolle
              </Label>
              <Select
                value={newUser.role}
                onValueChange={(value) =>
                  setNewUser({ ...newUser, role: value })
                }
              >
                <SelectTrigger id="role" className="col-span-3">
                  <SelectValue placeholder="Velg rolle" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ADMIN">Administrator</SelectItem>
                  <SelectItem value="DRIVER">Sjåfør</SelectItem>
                  <SelectItem value="CUSTOMER">Kunde</SelectItem>
                  <SelectItem value="USER">Bruker</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {/* Campo active removido pois não existe no modelo User */}
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowCreateDialog(false)}
              disabled={isSubmitting}
            >
              Avbryt
            </Button>
            <Button
              onClick={handleCreateUser}
              disabled={
                isSubmitting ||
                !newUser.name ||
                !newUser.email ||
                !newUser.password
              }
            >
              {isSubmitting ? "Oppretter..." : "Opprett bruker"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog for å redigere bruker */}
      {currentUser && (
        <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Rediger bruker</DialogTitle>
              <DialogDescription>
                Oppdater informasjonen for denne brukeren.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-name" className="text-right">
                  Navn
                </Label>
                <Input
                  id="edit-name"
                  value={currentUser.name}
                  onChange={(e) =>
                    setCurrentUser({ ...currentUser, name: e.target.value })
                  }
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-email" className="text-right">
                  E-post
                </Label>
                <Input
                  id="edit-email"
                  type="email"
                  value={currentUser.email}
                  onChange={(e) =>
                    setCurrentUser({ ...currentUser, email: e.target.value })
                  }
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-role" className="text-right">
                  Rolle
                </Label>
                <Select
                  value={currentUser.role}
                  onValueChange={(value) =>
                    setCurrentUser({ ...currentUser, role: value })
                  }
                >
                  <SelectTrigger id="edit-role" className="col-span-3">
                    <SelectValue placeholder="Velg rolle" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ADMIN">Administrator</SelectItem>
                    <SelectItem value="DRIVER">Sjåfør</SelectItem>
                    <SelectItem value="CUSTOMER">Kunde</SelectItem>
                    <SelectItem value="USER">Bruker</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              {/* Campo active removido pois não existe no modelo User */}
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setShowEditDialog(false)}
                disabled={isSubmitting}
              >
                Avbryt
              </Button>
              <Button
                onClick={handleUpdateUser}
                disabled={
                  isSubmitting || !currentUser.name || !currentUser.email
                }
              >
                {isSubmitting ? "Lagrer..." : "Lagre endringer"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Dialog for å slette bruker */}
      {currentUser && (
        <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Slett bruker</DialogTitle>
              <DialogDescription>
                Er du sikker på at du vil slette denne brukeren? Denne
                handlingen kan ikke angres.
              </DialogDescription>
            </DialogHeader>
            <div className="py-4">
              <p className="mb-2">Du er i ferd med å slette følgende bruker:</p>
              <div className="bg-muted p-3 rounded-md">
                <p>
                  <strong>Navn:</strong> {currentUser.name}
                </p>
                <p>
                  <strong>E-post:</strong> {currentUser.email}
                </p>
                <p>
                  <strong>Rolle:</strong> {translateRole(currentUser.role)}
                </p>
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setShowDeleteDialog(false)}
                disabled={isSubmitting}
              >
                Avbryt
              </Button>
              <Button
                variant="destructive"
                onClick={handleDeleteUser}
                disabled={isSubmitting}
              >
                {isSubmitting ? "Sletter..." : "Slett bruker"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
