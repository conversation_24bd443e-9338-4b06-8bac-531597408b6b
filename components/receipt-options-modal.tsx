"use client";

import React, { useRef, useEffect, useState } from "react";
import { <PERSON><PERSON>, Di<PERSON><PERSON>ontent, DialogHeader, DialogTitle } from "./ui/dialog";
import { Download, Mail, X } from "lucide-react";
import "@/styles/driver-modal.css";

interface ReceiptOptionsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onDownload: () => void;
  onSendEmail: () => void;
}

export function ReceiptOptionsModal({
  isOpen,
  onClose,
  onDownload,
  onSendEmail,
}: ReceiptOptionsModalProps) {
  const contentRef = useRef<HTMLDivElement>(null);
  const startY = useRef<number | null>(null);
  const currentY = useRef<number | null>(null);
  const [isClosing, setIsClosing] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Detectar se estamos em um dispositivo móvel
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 640);
    };

    // Verificar inicialmente
    checkMobile();

    // Adicionar listener para redimensionamento
    window.addEventListener("resize", checkMobile);

    // Limpar listener
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  // Função para lidar com o fechamento com animação
  const handleClose = () => {
    if (isMobile) {
      // Apenas em telas mobile (sm breakpoint é 640px)
      setIsClosing(true);
      setTimeout(() => {
        setIsClosing(false);
        onClose();
      }, 300); // Tempo da animação
    } else {
      onClose();
    }
  };

  // Função para lidar com o swipe (apenas em mobile)
  const handleTouchStart = (e: TouchEvent) => {
    if (!isMobile) return;
    startY.current = e.touches[0].clientY;
  };

  const handleTouchMove = (e: TouchEvent) => {
    if (!isMobile || !startY.current) return;
    currentY.current = e.touches[0].clientY;

    const diff = currentY.current - startY.current;

    // Só permitir swipe para baixo (não para cima)
    if (diff > 0) {
      if (contentRef.current) {
        contentRef.current.style.transform = `translateY(${diff}px)`;
      }
    }
  };

  const handleTouchEnd = () => {
    if (!isMobile || !startY.current || !currentY.current) return;

    const diff = currentY.current - startY.current;

    // Se o swipe for maior que 100px, fechar o modal
    if (diff > 100) {
      handleClose();
    }

    // Resetar a posição
    if (contentRef.current) {
      contentRef.current.style.transform = "";
    }

    startY.current = null;
    currentY.current = null;
  };

  // Adicionar e remover event listeners apenas em dispositivos móveis
  useEffect(() => {
    const content = contentRef.current;

    if (content && isOpen && isMobile) {
      content.addEventListener("touchstart", handleTouchStart);
      content.addEventListener("touchmove", handleTouchMove);
      content.addEventListener("touchend", handleTouchEnd);

      return () => {
        content.removeEventListener("touchstart", handleTouchStart);
        content.removeEventListener("touchmove", handleTouchMove);
        content.removeEventListener("touchend", handleTouchEnd);
      };
    }
  }, [isOpen, isMobile, handleTouchStart, handleTouchMove, handleTouchEnd]);

  return (
    <Dialog open={isOpen || isClosing} onOpenChange={handleClose}>
      <DialogContent
        ref={contentRef}
        className="p-0 overflow-auto sm:max-w-[400px] sm:h-auto sm:max-h-[450px] sm:rounded-lg max-sm:bottom-0 max-sm:top-auto max-sm:translate-y-0 max-sm:rounded-t-lg max-sm:h-auto max-sm:max-h-[55vh] max-sm:left-0 max-sm:right-0 max-sm:mx-auto max-sm:w-[95%] transition-transform duration-300 z-[110]"
        style={{
          animation: isMobile
            ? isClosing
              ? "slideDown 0.3s ease-out forwards"
              : isOpen
              ? "slideUp 0.3s ease-out forwards"
              : "none"
            : "none",
        }}
      >
        {/* Indicador de swipe - apenas em mobile */}
        <div className="w-12 h-1 bg-gray-300 dark:bg-gray-600 rounded-full mx-auto mt-2 mb-0 max-sm:block hidden" />

        <div className="flex flex-col pt-0 px-4 pb-4 sm:pt-4">
          <div className="w-full flex justify-between items-start mb-4">
            <div className="text-left">
              <DialogTitle className="text-xl text-left">
                Få kvittering
              </DialogTitle>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1 text-left">
                Velg ett av de tilgjengelige alternativene
              </p>
            </div>
            <button
              onClick={handleClose}
              className="opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground h-6 w-6 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 flex items-center justify-center ml-2"
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </button>
          </div>

          <div className="space-y-4">
            <button
              onClick={onDownload}
              className="w-full flex items-center p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            >
              <Download className="h-5 w-5 mr-3 text-gray-500 dark:text-gray-400" />
              <span>Last ned kvittering</span>
            </button>
            <button
              onClick={onSendEmail}
              className="w-full flex items-center p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            >
              <Mail className="h-5 w-5 mr-3 text-gray-500 dark:text-gray-400" />
              <span>Send kvittering på nytt</span>
            </button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
