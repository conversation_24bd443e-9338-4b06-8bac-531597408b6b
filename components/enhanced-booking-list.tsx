"use client";

import { useState, useMemo } from "react";
import { format } from "date-fns";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { BookingActions } from "@/components/booking-actions";
import { AdvancedFilters } from "@/components/advanced-filters";
import { Loader2 } from "lucide-react";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

type BookingStatus =
  | "PENDING"
  | "CONFIRMED"
  | "IN_PROGRESS"
  | "COMPLETED"
  | "CANCELLED";

interface Booking {
  id: string;
  scheduledTime: string | Date;
  pickupLocation: string;
  dropoffLocation: string;
  status: BookingStatus;
  vehicle: {
    id: string;
    model?: string;
    type: string | VehicleType;
    plateNumber: string;
    make?: string;
    year?: number;
    [key: string]: any;
  };
  customer: {
    id?: string;
    name: string;
    email: string;
    phone: string | null;
    [key: string]: any;
  };
  driver?: {
    id?: string;
    name: string;
    email: string;
    phone: string | null;
    [key: string]: any;
  } | null;
  [key: string]: any;
}

// Enum for kjøretøytyper
enum VehicleType {
  SEDAN = "SEDAN",
  SUV = "SUV",
  VAN = "VAN",
  MINIBUS = "MINIBUS",
  BUS = "BUS",
}

interface EnhancedBookingListProps {
  bookings: Booking[];
  userRole: "CUSTOMER" | "DRIVER" | "ADMIN";
  isLoading?: boolean;
}

export function EnhancedBookingList({
  bookings,
  userRole,
  isLoading = false,
}: EnhancedBookingListProps) {
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [filters, setFilters] = useState<Record<string, any>>({});
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  const statusColors = {
    PENDING: "bg-yellow-500",
    CONFIRMED: "bg-blue-500",
    IN_PROGRESS: "bg-green-500",
    COMPLETED: "bg-gray-500",
    CANCELLED: "bg-red-500",
  };

  // Opprette alternativer for filtre
  const statusOptions = [
    { id: "1", label: "Ventende", value: "PENDING" },
    { id: "2", label: "Bekreftet", value: "CONFIRMED" },
    { id: "3", label: "Pågår", value: "IN_PROGRESS" },
    { id: "4", label: "Fullført", value: "COMPLETED" },
    { id: "5", label: "Kansellert", value: "CANCELLED" },
  ];

  // Hente unike sjåfører og kjøretøy for filteralternativer
  const driverOptions = useMemo(() => {
    const uniqueDrivers = new Map();
    bookings.forEach((booking) => {
      if (booking.driver?.id) {
        uniqueDrivers.set(booking.driver.id, {
          id: booking.driver.id,
          label: booking.driver.name,
          value: booking.driver.id,
        });
      }
    });
    return Array.from(uniqueDrivers.values());
  }, [bookings]);

  const vehicleOptions = useMemo(() => {
    const uniqueVehicles = new Map();
    bookings.forEach((booking) => {
      if (booking.vehicle?.id) {
        uniqueVehicles.set(booking.vehicle.id, {
          id: booking.vehicle.id,
          label:
            booking.vehicle.model ||
            `${booking.vehicle.type} (${booking.vehicle.plateNumber})`,
          value: booking.vehicle.id,
        });
      }
    });
    return Array.from(uniqueVehicles.values());
  }, [bookings]);

  // Filtrere bestillinger basert på valgte filtre
  const filteredBookings = useMemo(() => {
    return bookings.filter((booking) => {
      // Søkefilter
      if (
        filters.search &&
        !(
          booking.pickupLocation
            ?.toLowerCase()
            .includes(filters.search.toLowerCase()) ||
          booking.dropoffLocation
            ?.toLowerCase()
            .includes(filters.search.toLowerCase()) ||
          booking.customer?.name
            ?.toLowerCase()
            .includes(filters.search.toLowerCase()) ||
          booking.driver?.name
            ?.toLowerCase()
            .includes(filters.search.toLowerCase()) ||
          booking.vehicle?.model
            ?.toLowerCase()
            .includes(filters.search.toLowerCase()) ||
          booking.vehicle?.plateNumber
            ?.toLowerCase()
            .includes(filters.search.toLowerCase())
        )
      ) {
        return false;
      }

      // Statusfilter
      if (filters.status && booking.status !== filters.status) {
        return false;
      }

      // Sjåførfilter
      if (filters.driver && booking.driver?.id !== filters.driver) {
        return false;
      }

      // Kjøretøyfilter
      if (filters.vehicle && booking.vehicle?.id !== filters.vehicle) {
        return false;
      }

      // Datoperiodefilter
      if (filters.dateFrom || filters.dateTo) {
        const bookingDate = new Date(booking.scheduledTime);
        if (filters.dateFrom && bookingDate < filters.dateFrom) {
          return false;
        }
        if (filters.dateTo) {
          const endDate = new Date(filters.dateTo);
          endDate.setHours(23, 59, 59, 999);
          if (bookingDate > endDate) {
            return false;
          }
        }
      }

      return true;
    });
  }, [bookings, filters]);

  // Paginering
  const totalPages = Math.ceil(filteredBookings.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredBookings.slice(
    indexOfFirstItem,
    indexOfLastItem
  );

  // Håndtere sideendring
  const handlePageChange = (pageNumber: number, e?: React.MouseEvent) => {
    // Prevent default behavior to avoid scrolling to top
    if (e) {
      e.preventDefault();
    }
    setCurrentPage(pageNumber);
  };

  return (
    <>
      {isLoading ? (
        <div className="flex items-center justify-center p-8 border rounded-md">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Laster bestillinger...</span>
        </div>
      ) : (
        <div className="rounded-md border overflow-hidden w-full">
          <div className="overflow-x-auto w-full">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="whitespace-nowrap">Dato</TableHead>
                  <TableHead className="whitespace-nowrap hidden md:table-cell">
                    Fra
                  </TableHead>
                  <TableHead className="whitespace-nowrap hidden md:table-cell">
                    Til
                  </TableHead>
                  <TableHead className="whitespace-nowrap">Status</TableHead>
                  <TableHead className="whitespace-nowrap hidden sm:table-cell">
                    Kjøretøy
                  </TableHead>
                  {userRole === "ADMIN" && (
                    <TableHead className="whitespace-nowrap hidden lg:table-cell">
                      Kunde
                    </TableHead>
                  )}
                  {(userRole === "ADMIN" || userRole === "CUSTOMER") && (
                    <TableHead className="whitespace-nowrap hidden lg:table-cell">
                      Sjåfør
                    </TableHead>
                  )}
                  <TableHead className="text-right whitespace-nowrap">
                    Handlinger
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {currentItems.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={8}
                      className="text-center py-6 text-muted-foreground"
                    >
                      {Object.keys(filters).length > 0
                        ? "Ingen bestillinger funnet med valgte filtre"
                        : "Ingen bestillinger funnet"}
                    </TableCell>
                  </TableRow>
                ) : (
                  currentItems.map((booking) => (
                    <TableRow key={booking.id}>
                      <TableCell className="whitespace-nowrap font-medium">
                        {format(
                          new Date(booking.scheduledTime),
                          "dd.MM.yyyy HH:mm"
                        )}
                      </TableCell>
                      <TableCell className="hidden md:table-cell">
                        {booking.pickupLocation}
                      </TableCell>
                      <TableCell className="hidden md:table-cell">
                        {booking.dropoffLocation}
                      </TableCell>
                      <TableCell>
                        <Badge className={statusColors[booking.status]}>
                          {booking.status}
                        </Badge>
                      </TableCell>
                      <TableCell className="hidden sm:table-cell">
                        {booking.vehicle.model ||
                          (booking.vehicle.type && booking.vehicle.plateNumber
                            ? `${booking.vehicle.type} (${booking.vehicle.plateNumber})`
                            : "Ikke spesifisert")}
                      </TableCell>
                      {userRole === "ADMIN" && (
                        <TableCell className="hidden lg:table-cell">
                          {booking.customer.name}
                        </TableCell>
                      )}
                      {(userRole === "ADMIN" || userRole === "CUSTOMER") && (
                        <TableCell className="hidden lg:table-cell">
                          {booking.driver?.name || "Ikke tildelt"}
                        </TableCell>
                      )}
                      <TableCell className="text-right">
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setSelectedBooking(booking)}
                              className="whitespace-nowrap"
                            >
                              Detaljer
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="sm:max-w-[500px]">
                            <DialogHeader>
                              <DialogTitle>Bestillingsdetaljer</DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4">
                              {selectedBooking && (
                                <BookingDetails booking={selectedBooking} />
                              )}
                              {selectedBooking && (
                                <BookingActions
                                  booking={selectedBooking}
                                  userRole={userRole}
                                  onStatusChange={async () => {}}
                                  onDriverAssign={async () => {}}
                                  onVehicleAssign={async () => {}}
                                />
                              )}
                            </div>
                          </DialogContent>
                        </Dialog>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </div>
      )}

      <div className="mt-4 flex flex-col sm:flex-row justify-end items-center gap-2">
        {totalPages > 1 && (
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={(e) =>
                    handlePageChange(Math.max(1, currentPage - 1), e)
                  }
                  className={
                    currentPage === 1 ? "pointer-events-none opacity-50" : ""
                  }
                  href="#"
                />
              </PaginationItem>

              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                // Logikk for å vise riktige sidetall basert på gjeldende side
                let pageNum;
                if (totalPages <= 5) {
                  // Hvis det er 5 eller færre sider, vis alle
                  pageNum = i + 1;
                } else if (currentPage <= 3) {
                  // Hvis gjeldende side er blant de første 3, vis 1-5
                  pageNum = i + 1;
                } else if (currentPage >= totalPages - 2) {
                  // Hvis gjeldende side er blant de siste 3, vis de siste 5
                  pageNum = totalPages - 4 + i;
                } else {
                  // Ellers vis 2 sider før og 2 sider etter gjeldende side
                  pageNum = currentPage - 2 + i;
                }

                return (
                  <PaginationItem key={pageNum}>
                    <PaginationLink
                      isActive={currentPage === pageNum}
                      onClick={(e) => handlePageChange(pageNum, e)}
                      href="#"
                    >
                      {pageNum}
                    </PaginationLink>
                  </PaginationItem>
                );
              })}

              <PaginationItem>
                <PaginationNext
                  onClick={(e) =>
                    handlePageChange(Math.min(totalPages, currentPage + 1), e)
                  }
                  className={
                    currentPage === totalPages
                      ? "pointer-events-none opacity-50"
                      : ""
                  }
                  href="#"
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        )}
      </div>
    </>
  );
}

interface BookingDetailsProps {
  booking: Booking;
}

function BookingDetails({ booking }: BookingDetailsProps) {
  if (!booking) return null;

  const details = [
    {
      label: "Tidspunkt",
      value: format(new Date(booking.scheduledTime), "dd.MM.yyyy HH:mm"),
    },
    { label: "Fra", value: booking.pickupLocation },
    { label: "Til", value: booking.dropoffLocation },
    {
      label: "Kjøretøy",
      value:
        booking.vehicle.model ||
        (booking.vehicle.type && booking.vehicle.plateNumber
          ? `${booking.vehicle.type} (${booking.vehicle.plateNumber})`
          : "Ikke spesifisert"),
    },
    { label: "Status", value: booking.status },
    { label: "Kunde", value: booking.customer?.name || "Ikke spesifisert" },
    { label: "Sjåfør", value: booking.driver?.name || "Ikke tildelt" },
  ];

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
        {details.map((detail, index) => (
          <div key={index} className="p-3 bg-muted/50 rounded-md">
            <div className="text-xs text-muted-foreground mb-1">
              {detail.label}
            </div>
            <div className="font-medium truncate">{detail.value}</div>
          </div>
        ))}
      </div>
    </div>
  );
}
