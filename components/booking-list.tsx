"use client";

import { useState } from "react";
import { format } from "date-fns";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { BookingActions } from "@/components/booking-actions";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

type BookingStatus =
  | "PENDING"
  | "CONFIRMED"
  | "IN_PROGRESS"
  | "COMPLETED"
  | "CANCELLED";

interface Booking {
  id: string;
  status: BookingStatus;
  pickupLocation: string;
  dropoffLocation: string;
  scheduledTime: string | Date;
  vehicle: {
    type?: string;
    plateNumber?: string;
    model?: string;
  };
  customer?: {
    name: string;
  };
  driver?: {
    name: string;
    email?: string;
    phone?: string | null;
  } | null;
}

interface BookingListProps {
  bookings: Booking[];
  userRole: "CUSTOMER" | "DRIVER" | "ADMIN";
}

export function BookingList({ bookings, userRole }: BookingListProps) {
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  const statusColors = {
    PENDING: "bg-yellow-500",
    CONFIRMED: "bg-blue-500",
    IN_PROGRESS: "bg-green-500",
    COMPLETED: "bg-gray-500",
    CANCELLED: "bg-red-500",
  };

  // Paginering
  const totalPages = Math.ceil(bookings.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = bookings.slice(indexOfFirstItem, indexOfLastItem);

  // Håndtere sideendring
  const handlePageChange = (pageNumber: number, e?: React.MouseEvent) => {
    // Prevent default behavior to avoid scrolling to top
    if (e) {
      e.preventDefault();
    }
    setCurrentPage(pageNumber);
  };

  const handleStatusChange = (
    bookingId: string,
    newStatus: string
  ): Promise<void> => {
    return new Promise((resolve) => {
      console.log(`Status da reserva ${bookingId} alterado para ${newStatus}`);
      resolve();
    });
  };

  const handleDriverAssign = (
    bookingId: string,
    driverId: string
  ): Promise<void> => {
    return new Promise((resolve) => {
      console.log(`Motorista ${driverId} atribuído à reserva ${bookingId}`);
      resolve();
    });
  };

  const handleVehicleAssign = (
    bookingId: string,
    vehicleId: string
  ): Promise<void> => {
    return new Promise((resolve) => {
      console.log(`Veículo ${vehicleId} atribuído à reserva ${bookingId}`);
      resolve();
    });
  };

  return (
    <>
      <div className="rounded-md border overflow-hidden w-full">
        <div className="overflow-x-auto w-full">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="whitespace-nowrap">Dato</TableHead>
                <TableHead className="whitespace-nowrap hidden md:table-cell">
                  Fra
                </TableHead>
                <TableHead className="whitespace-nowrap hidden md:table-cell">
                  Til
                </TableHead>
                <TableHead className="whitespace-nowrap">Status</TableHead>
                <TableHead className="whitespace-nowrap hidden sm:table-cell">
                  Kjøretøy
                </TableHead>
                {userRole === "ADMIN" && (
                  <TableHead className="whitespace-nowrap hidden lg:table-cell">
                    Kunde
                  </TableHead>
                )}
                {(userRole === "ADMIN" || userRole === "CUSTOMER") && (
                  <TableHead className="whitespace-nowrap hidden lg:table-cell">
                    Sjåfør
                  </TableHead>
                )}
                <TableHead className="text-right whitespace-nowrap">
                  Handlinger
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentItems.length === 0 ? (
                <TableRow>
                  <TableCell
                    colSpan={8}
                    className="text-center py-6 text-muted-foreground"
                  >
                    Ingen bestillinger funnet
                  </TableCell>
                </TableRow>
              ) : (
                currentItems.map((booking) => (
                  <TableRow key={booking.id}>
                    <TableCell className="whitespace-nowrap font-medium">
                      {format(
                        new Date(booking.scheduledTime),
                        "dd.MM.yyyy HH:mm"
                      )}
                    </TableCell>
                    <TableCell className="hidden md:table-cell">
                      {booking.pickupLocation}
                    </TableCell>
                    <TableCell className="hidden md:table-cell">
                      {booking.dropoffLocation}
                    </TableCell>
                    <TableCell>
                      <Badge className={statusColors[booking.status]}>
                        {booking.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="hidden sm:table-cell">
                      {booking.vehicle.model ||
                        (booking.vehicle.type && booking.vehicle.plateNumber
                          ? `${booking.vehicle.type} (${booking.vehicle.plateNumber})`
                          : "Ikke spesifisert")}
                    </TableCell>
                    {userRole === "ADMIN" && (
                      <TableCell className="hidden lg:table-cell">
                        {booking.customer?.name}
                      </TableCell>
                    )}
                    {(userRole === "ADMIN" || userRole === "CUSTOMER") && (
                      <TableCell className="hidden lg:table-cell">
                        {booking.driver?.name || "Ikke tildelt"}
                      </TableCell>
                    )}
                    <TableCell className="text-right">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setSelectedBooking(booking)}
                            className="whitespace-nowrap"
                          >
                            Detaljer
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="sm:max-w-[500px]">
                          <DialogHeader>
                            <DialogTitle>Bestillingsdetaljer</DialogTitle>
                          </DialogHeader>
                          <div className="space-y-4">
                            <BookingDetails booking={selectedBooking} />
                            {selectedBooking && (
                              <BookingActions
                                booking={selectedBooking}
                                userRole={userRole}
                                onStatusChange={handleStatusChange}
                                onDriverAssign={handleDriverAssign}
                                onVehicleAssign={handleVehicleAssign}
                              />
                            )}
                          </div>
                        </DialogContent>
                      </Dialog>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      <div className="mt-4 flex flex-col sm:flex-row justify-end items-center gap-2">
        {totalPages > 1 && (
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={(e) =>
                    handlePageChange(Math.max(1, currentPage - 1), e)
                  }
                  className={
                    currentPage === 1 ? "pointer-events-none opacity-50" : ""
                  }
                  href="#"
                />
              </PaginationItem>

              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                // Logikk for å vise riktige sidetall basert på gjeldende side
                let pageNum;
                if (totalPages <= 5) {
                  // Hvis det er 5 eller færre sider, vis alle
                  pageNum = i + 1;
                } else if (currentPage <= 3) {
                  // Hvis gjeldende side er blant de første 3, vis 1-5
                  pageNum = i + 1;
                } else if (currentPage >= totalPages - 2) {
                  // Hvis gjeldende side er blant de siste 3, vis de siste 5
                  pageNum = totalPages - 4 + i;
                } else {
                  // Ellers vis 2 sider før og 2 sider etter gjeldende side
                  pageNum = currentPage - 2 + i;
                }

                return (
                  <PaginationItem key={pageNum}>
                    <PaginationLink
                      isActive={currentPage === pageNum}
                      onClick={(e) => handlePageChange(pageNum, e)}
                      href="#"
                    >
                      {pageNum}
                    </PaginationLink>
                  </PaginationItem>
                );
              })}

              <PaginationItem>
                <PaginationNext
                  onClick={(e) =>
                    handlePageChange(Math.min(totalPages, currentPage + 1), e)
                  }
                  className={
                    currentPage === totalPages
                      ? "pointer-events-none opacity-50"
                      : ""
                  }
                  href="#"
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        )}
      </div>
    </>
  );
}

function BookingDetails({ booking }: { booking: Booking | null }) {
  if (!booking) return null;

  const details = [
    {
      label: "Tidspunkt",
      value: format(new Date(booking.scheduledTime), "dd.MM.yyyy HH:mm"),
    },
    { label: "Fra", value: booking.pickupLocation },
    { label: "Til", value: booking.dropoffLocation },
    {
      label: "Kjøretøy",
      value:
        booking.vehicle.model ||
        (booking.vehicle.type && booking.vehicle.plateNumber
          ? `${booking.vehicle.type} (${booking.vehicle.plateNumber})`
          : "Ikke spesifisert"),
    },
    { label: "Status", value: booking.status },
    { label: "Kunde", value: booking.customer?.name || "Ikke spesifisert" },
    { label: "Sjåfør", value: booking.driver?.name || "Ikke tildelt" },
  ];

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
        {details.map((detail, index) => (
          <div key={index} className="p-3 bg-muted/50 rounded-md">
            <div className="text-xs text-muted-foreground mb-1">
              {detail.label}
            </div>
            <div className="font-medium truncate">{detail.value}</div>
          </div>
        ))}
      </div>
    </div>
  );
}
