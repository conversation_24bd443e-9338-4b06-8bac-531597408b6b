"use client";

import { useEffect } from "react";
import { syncDriverData, forceSyncDriverData } from "@/lib/sync-driver-data";
import { updateMockDataFromLocalStorage } from "@/lib/initialize-mock-data";

interface SyncDriverDataInRideDetailsProps {
  driverId?: string;
}

export function SyncDriverDataInRideDetails({
  driverId,
}: SyncDriverDataInRideDetailsProps) {
  useEffect(() => {
    // Sincronizar os dados do driver quando o componente for montado
    console.log("SyncDriverDataInRideDetails: Syncing driver data...");

    // Atualizar os dados mockados com os dados do localStorage
    updateMockDataFromLocalStorage();
    console.log(
      "SyncDriverDataInRideDetails: Updated mock data from localStorage"
    );

    // Forçar a sincronização dos dados do driver
    forceSyncDriverData();
    console.log("SyncDriverDataInRideDetails: Forced driver data sync");
  }, [driverId]);

  // Este componente não renderiza nada
  return null;
}
