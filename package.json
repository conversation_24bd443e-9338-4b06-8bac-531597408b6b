{"name": "viken-tours", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:seed": "ts-node prisma/seed.ts", "docker:up": "docker compose up -d", "docker:down": "docker compose down", "docker:logs": "docker compose logs -f", "db:studio": "prisma studio", "db:push": "prisma db push", "db:reset": "prisma migrate reset", "db:export": "node scripts/export-sqlite-data.js", "db:import": "node scripts/import-to-postgres.js", "db:migrate-to-postgres": "node scripts/migrate-to-postgres.js", "setup": "node setup.mjs"}, "dependencies": {"@auth/prisma-adapter": "^1.0.9", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^3.3.4", "@prisma/client": "^6.6.0", "@prisma/extension-accelerate": "^1.3.0", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-context-menu": "^2.1.5", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.11", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.2.0", "@types/dotenv": "^6.1.1", "@types/minio": "^7.1.0", "@types/react-big-calendar": "^1.16.1", "@types/uuid": "^10.0.0", "bcryptjs": "^2.4.3", "chart.js": "^4.4.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "embla-carousel-react": "^8.0.0-rc22", "input-otp": "^1.1.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lodash": "^4.17.21", "lucide-react": "^0.359.0", "minio": "^8.0.5", "next": "14.2.4", "next-auth": "^5.0.0-beta.15", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "pg": "^8.14.1", "qrcode": "^1.5.4", "react": "^18.2.0", "react-big-calendar": "^1.18.0", "react-chartjs-2": "^5.3.0", "react-day-picker": "^9.6.6", "react-dom": "^18.2.0", "react-easy-crop": "^5.4.1", "react-hook-form": "^7.51.0", "react-hot-toast": "^2.4.1", "react-resizable-panels": "^2.0.12", "recharts": "^2.15.2", "sonner": "^2.0.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^0.9.0", "zod": "^3.22.4", "zustand": "^4.5.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/lodash": "^4.17.16", "@types/node": "^20.11.25", "@types/qrcode": "^1.5.5", "@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "autoprefixer": "^10.4.18", "eslint": "^8.57.0", "eslint-config-next": "14.2.4", "postcss": "^8.4.35", "prisma": "^5.22.0", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5.4.2"}}