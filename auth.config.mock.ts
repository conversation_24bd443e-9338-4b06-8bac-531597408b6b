import type { NextAuthConfig } from "next-auth";
import Credentials from "next-auth/providers/credentials";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";

// Definir os tipos de roles como strings para evitar problemas com o enum
type UserRole = "ADMIN" | "DRIVER" | "CUSTOMER";

// Função para gerar um ID único
function generateId() {
  return uuidv4();
}

// Faste brukere for testing - Apen<PERSON> as contas de exemplo mencionadas no README.md
const mockUsers = [
  {
    id: "admin1",
    email: "<EMAIL>",
    name: "Administrator",
    passwordHash:
      "$2a$10$Ql0T/Jj9qU8wOAMQ9xDYx.vhKFyxK7IEJ9.R.LWW/fBgQ8AutYqfO", // admin12345
    role: "ADMIN" as UserRole,
    bookings: [],
  },
  {
    id: "driver1",
    email: "<EMAIL>", // Oppdatert for å matche innloggingsinstruksjoner
    name: "Test 1 Hansen",
    passwordHash:
      "$2a$10$Ql0T/Jj9qU8wOAMQ9xDYx.vhKFyxK7IEJ9.R.LWW/fBgQ8AutYqfO", // admin12345
    role: "DRIVER" as UserRole,
    bookings: [],
  },
  {
    id: "customer1",
    email: "<EMAIL>",
    name: "John Doe",
    passwordHash:
      "$2a$10$Ql0T/Jj9qU8wOAMQ9xDYx.vhKFyxK7IEJ9.R.LWW/fBgQ8AutYqfO", // admin12345
    role: "CUSTOMER" as UserRole,
    bookings: [],
  },
];

// Função para adicionar um novo usuário
export function addMockUser(email: string, name: string, role: UserRole) {
  const existingUser = mockUsers.find((user) => user.email === email);
  if (existingUser) {
    return existingUser;
  }

  const newUser = {
    id: generateId(),
    email,
    name,
    passwordHash:
      "$2a$10$Ql0T/Jj9qU8wOAMQ9xDYx.vhKFyxK7IEJ9.R.LWW/fBgQ8AutYqfO",
    role,
    bookings: [],
  };

  mockUsers.push(newUser);
  return newUser;
}

// Função para obter um usuário pelo email
export function getMockUserByEmail(email: string) {
  return mockUsers.find((user) => user.email === email);
}

// Função para obter um usuário pelo ID
export function getMockUserById(id: string) {
  // Verificar se o ID existe na lista de usuários mockados
  console.log(
    `auth.config.mock.ts - getMockUserById - Getting user with ID: ${id}`
  );

  // Verificar se temos dados do driver no localStorage (se estiver no navegador)
  if (typeof window !== "undefined") {
    try {
      const driverProfileStr = localStorage.getItem("driverProfile");
      if (driverProfileStr && (id === "driver1" || id.includes("driver"))) {
        console.log(
          "Found driver profile in localStorage, checking if it matches"
        );
        const driverProfile = JSON.parse(driverProfileStr);

        // Se o ID do perfil no localStorage corresponder ao ID solicitado, atualizar o usuário mockado
        if (driverProfile.id === id || driverProfile.id === "driver1") {
          console.log("Updating mock user from localStorage before returning");

          // Encontrar o índice do driver nos dados mockados
          const driverIndex = mockUsers.findIndex((u) => u.id === "driver1");
          if (driverIndex !== -1) {
            // Atualizar os dados do driver
            mockUsers[driverIndex].name =
              driverProfile.name || mockUsers[driverIndex].name;
            mockUsers[driverIndex].phone =
              driverProfile.phone || mockUsers[driverIndex].phone;
            mockUsers[driverIndex].image =
              driverProfile.image || mockUsers[driverIndex].image;

            console.log(
              `Updated mock user from localStorage: ${mockUsers[driverIndex].name}`
            );
          }
        }
      }
    } catch (e) {
      console.error("Error loading driver profile from localStorage:", e);
    }
  }

  const user = mockUsers.find((user) => user.id === id);

  if (user) {
    console.log(`Found user: ${user.name}, role: ${user.role}`);
  } else {
    console.log(`User with ID ${id} not found`);
  }

  // Não criar usuários automaticamente, apenas retornar o usuário se existir
  return user;
}

// Exportar a lista de usuários mockados para uso em outros arquivos
export { mockUsers };

export default {
  providers: [
    Credentials({
      async authorize(credentials) {
        const parsedCredentials = z
          .object({ email: z.string().email(), password: z.string().min(6) })
          .safeParse(credentials);

        if (!parsedCredentials.success) return null;

        const { email } = parsedCredentials.data;

        // Søke etter bruker i testdataene
        const user = mockUsers.find((u) => u.email === email);

        // Se o usuário não existir na lista de usuários mockados, não criar automaticamente
        if (!user) {
          console.log(`Usuário não encontrado nos dados mockados: ${email}`);
          // Não criar um novo usuário mockado, deixar o sistema usar o banco de dados real
          return null;
        }

        if (!user?.passwordHash) return null;

        // For å forenkle, godtar vi hvilket som helst passord for testbrukere
        // I produksjon bør du bruke bcrypt.compare
        // const passwordsMatch = await bcrypt.compare(password, user.passwordHash);
        const passwordsMatch = true; // Godtar hvilket som helst passord for å forenkle testing

        if (!passwordsMatch) return null;

        console.log(`Usuário autenticado: ${user.email}, ID: ${user.id}`);

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
        };
      },
    }),
  ],
  pages: {
    signIn: "/auth/login",
    signOut: "/auth/logout",
    error: "/auth/error",
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role;
        token.id = user.id;
        token.email = user.email;

        // Verificar se é o driver de exemplo pelo email
        if (user.email === "<EMAIL>") {
          console.log(
            "JWT - Driver de exemplo detectado, garantindo ID correto"
          );
          token.id = "driver1";
        }

        console.log("JWT - User:", user);
        console.log("JWT - Token:", token);
      }
      return token;
    },
    async session({ session, token }) {
      if (token && session.user) {
        // Legge til brukerens ID i sesjonen
        if (token.id) {
          session.user.id = token.id as string;

          // Verificar se o ID do usuário é o do driver de exemplo
          if (
            token.id === "driver1" ||
            (typeof token.email === "string" &&
              token.email === "<EMAIL>")
          ) {
            console.log(
              "Session - Driver de exemplo detectado, garantindo ID correto"
            );
            session.user.id = "driver1";
          }
        }

        // Sjekke om verdien av token.role er en gyldig rolle
        if (typeof token.role === "string") {
          // Manuell sjekk av gyldige roller for å unngå problemer med UserRole enum i middleware
          const validRoles = ["ADMIN", "DRIVER", "CUSTOMER"];
          if (validRoles.includes(token.role)) {
            session.user.role = token.role as UserRole; // Bruke typeassertion for å sikre kompatibilitet
          }
        }

        console.log("Session - Token:", token);
        console.log("Session - Session:", session);
        console.log("Session - User ID:", session.user.id);
        console.log("Session - User Email:", session.user.email);
        console.log("Session - User Role:", session.user.role);
      }
      return session;
    },
  },
} satisfies NextAuthConfig;
