// Script para atualizar as informações do motorista nos dados mockados
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

// Obter o diretório atual
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Caminho para o arquivo mock-data.ts
const mockDataPath = path.join(__dirname, "..", "lib", "mock-data.ts");

// Ler o conteúdo do arquivo
let mockData = fs.readFileSync(mockDataPath, "utf8");

// Informações atualizadas do motorista
const driverName = "Test 1 Hansen";
const driverPhone = "+47 99452211";
const driverImage =
  "http://localhost:9000/viken-tours/profile-images/1746598119314-italo1.png";

// Substituir todas as ocorrências do nome do motorista
mockData = mockData.replace(/name: "<PERSON>"/g, `name: "${driverName}"`);

// Substituir todas as ocorrências do telefone do motorista (ambos os formatos)
mockData = mockData.replace(
  /phone: "\+47 987 65 432"/g,
  `phone: "${driverPhone}"`
);
mockData = mockData.replace(
  /phone: "\+47 876 54 321"/g,
  `phone: "${driverPhone}"`
);

// Adicionar a imagem do motorista a todas as ocorrências do objeto driver
const driverRegex =
  /(driver: \{\s+id: "driver1",\s+email: "driver@vikentours\.no",\s+name: "[^"]+",\s+phone: "[^"]+",\s+role: "DRIVER" as UserRole,)(?!\s+image:)/g;
const driverReplacement = `$1\n      image: "${driverImage}",`;
mockData = mockData.replace(driverRegex, driverReplacement);

// Escrever o conteúdo atualizado de volta para o arquivo
fs.writeFileSync(mockDataPath, mockData, "utf8");

console.log("Dados mockados atualizados com sucesso!");
