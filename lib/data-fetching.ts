import { db } from "@/lib/db";
import { cache } from "react";
import * as mockDataFetching from "@/lib/data-fetching-mock";
import { useMockData } from "@/lib/use-mock-data";
import { loadMockDataFromStorage } from "@/lib/load-mock-data";

// Bufrede datafunksjoner for å forbedre ytelsen
export const getBookings = cache(async (limit?: number) => {
  // Bruk mockdata hvis USE_MOCK_DATA er satt til true
  if (useMockData()) {
    return mockDataFetching.getBookings(limit);
  }

  return db.booking.findMany({
    include: {
      vehicle: true,
      customer: {
        select: {
          name: true,
          email: true,
          phone: true,
        },
      },
      driver: {
        select: {
          name: true,
          email: true,
          phone: true,
        },
      },
    },
    orderBy: {
      scheduledTime: "desc",
    },
    ...(limit ? { take: limit } : {}),
  });
});

export const getBookingStats = cache(async () => {
  // Bruk mockdata hvis USE_MOCK_DATA er satt til true
  if (useMockData()) {
    return mockDataFetching.getBookingStats();
  }

  const [
    totalBookings,
    pendingBookings,
    confirmedBookings,
    completedBookings,
    cancelledBookings,
  ] = await Promise.all([
    db.booking.count(),
    db.booking.count({ where: { status: "PENDING" } }),
    db.booking.count({ where: { status: "CONFIRMED" } }),
    db.booking.count({ where: { status: "COMPLETED" } }),
    db.booking.count({ where: { status: "CANCELLED" } }),
  ]);

  return {
    totalBookings,
    pendingBookings,
    confirmedBookings,
    completedBookings,
    cancelledBookings,
  };
});

// Removido o cache para garantir que sempre obtemos os dados mais recentes
export const getDrivers = async () => {
  // Carregar os dados do localStorage se estiver no navegador
  if (typeof window !== "undefined") {
    try {
      console.log("getDrivers - Loading mock data from localStorage");
      loadMockDataFromStorage();
    } catch (e) {
      console.error("Error loading mock data from localStorage:", e);
    }
  }

  // Bruk mockdata hvis USE_MOCK_DATA er satt til true
  if (useMockData()) {
    console.log("data-fetching.ts - Using mock data for drivers");
    const drivers = await mockDataFetching.getDrivers();
    console.log(`Retrieved ${drivers.length} drivers from mock data`);

    // Verificar se o driver de exemplo tem os dados corretos
    const exampleDriver = drivers.find(
      (d) => d.email === "<EMAIL>"
    );
    if (exampleDriver) {
      console.log(`Example driver found: ${exampleDriver.name}`);
      console.log(
        `Example driver image: ${
          exampleDriver.image
            ? exampleDriver.image.substring(0, 30) + "..."
            : "None"
        }`
      );
      // @ts-ignore - Verificando propriedade driverProfile
      if (exampleDriver.driverProfile) {
        console.log("Example driver has profile data");
      }
    }

    return drivers;
  }

  return db.user.findMany({
    where: {
      role: "DRIVER",
    },
    orderBy: {
      name: "asc",
    },
  });
};

// Removido o cache para garantir que sempre obtemos os dados mais recentes
export const getUsers = async () => {
  // Carregar os dados do localStorage se estiver no navegador
  if (typeof window !== "undefined") {
    try {
      console.log("getUsers - Loading mock data from localStorage");
      loadMockDataFromStorage();
    } catch (e) {
      console.error("Error loading mock data from localStorage:", e);
    }
  }

  // Bruk mockdata hvis USE_MOCK_DATA er satt til true
  if (useMockData()) {
    console.log("data-fetching.ts - Using mock data for users");
    const users = await mockDataFetching.getUsers();
    console.log(`Retrieved ${users.length} users from mock data`);

    // Verificar se o driver de exemplo tem os dados corretos
    const exampleDriver = users.find((u) => u.email === "<EMAIL>");
    if (exampleDriver) {
      console.log(`Example driver found in users: ${exampleDriver.name}`);
      console.log(
        `Example driver image: ${
          exampleDriver.image
            ? exampleDriver.image.substring(0, 30) + "..."
            : "None"
        }`
      );
      // @ts-ignore - Verificando propriedade driverProfile
      if (exampleDriver.driverProfile) {
        console.log("Example driver has profile data in users list");
      }
    }

    return users;
  }

  return db.user.findMany({
    orderBy: {
      createdAt: "desc",
    },
    select: {
      id: true,
      name: true,
      email: true,
      role: true,
      createdAt: true,
      updatedAt: true,
      phone: true,
    },
  });
};

// Implementação segura da função getVehicles
export const getVehicles = async () => {
  try {
    // Bruk mockdata hvis USE_MOCK_DATA er satt til true
    if (useMockData()) {
      console.log("data-fetching.ts - Using mock data for vehicles");
      return mockDataFetching.getVehicles();
    }

    // Tente buscar os veículos do banco de dados com um timeout
    const vehicles = await Promise.race([
      db.vehicle.findMany({
        orderBy: {
          type: "asc",
        },
      }),
      new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Database timeout')), 2000);
      })
    ]);

    return vehicles || [];
  } catch (error) {
    console.error("Error fetching vehicles:", error);
    // Retornar uma lista vazia em caso de erro
    return [];
  }
};

export const getDriverStats = cache(async () => {
  // Bruk mockdata hvis USE_MOCK_DATA er satt til true
  if (useMockData()) {
    return mockDataFetching.getDriverStats();
  }

  // Siden det ikke finnes noe isActive-felt i User-modellen,
  // vil vi bare telle alle sjåfører som aktive for nå
  const totalDrivers = await db.user.count({ where: { role: "DRIVER" } });

  return {
    totalDrivers,
    activeDrivers: totalDrivers, // Antar at alle sjåfører er aktive
    inactiveDrivers: 0, // Ingen inaktive sjåfører for øyeblikket
  };
});

export const getVehicleStats = cache(async () => {
  // Bruk mockdata hvis USE_MOCK_DATA er satt til true
  if (useMockData()) {
    return mockDataFetching.getVehicleStats();
  }

  const [totalVehicles, availableVehicles] = await Promise.all([
    db.vehicle.count(),
    db.vehicle.count({ where: { isAvailable: true } }),
  ]);

  return {
    totalVehicles,
    availableVehicles,
    unavailableVehicles: totalVehicles - availableVehicles,
  };
});

// Spesifikke funksjoner for sjåførens dashbord
// Removido o cache para garantir que sempre obtemos os dados mais recentes
export const getDriverBookings = async (driverId?: string) => {
  if (!driverId) {
    console.warn("getDriverBookings kalt uten en gyldig driverId");
    return [];
  }

  // Carregar os dados do localStorage se estiver no navegador
  if (typeof window !== "undefined") {
    try {
      console.log("getDriverBookings - Loading mock data from localStorage");
      loadMockDataFromStorage();
    } catch (e) {
      console.error("Error loading mock data from localStorage:", e);
    }
  }

  // Bruk mockdata hvis USE_MOCK_DATA er satt til true
  if (useMockData()) {
    console.log("data-fetching.ts - Using mock data for driver bookings");
    const bookings = await mockDataFetching.getDriverBookings(driverId);
    console.log(`Retrieved ${bookings.length} bookings for driver ${driverId}`);

    // Verificar se as corridas têm os dados corretos do driver
    if (bookings.length > 0 && bookings[0].driver) {
      const firstBooking = bookings[0];
      console.log(`First booking driver: ${firstBooking.driver.name}`);
      console.log(
        `First booking driver image: ${
          firstBooking.driver.image
            ? firstBooking.driver.image.substring(0, 30) + "..."
            : "None"
        }`
      );
    }

    return bookings;
  }

  return db.booking.findMany({
    where: {
      driverId: driverId,
    },
    include: {
      vehicle: true,
      customer: {
        select: {
          name: true,
          email: true,
          phone: true,
        },
      },
    },
    orderBy: {
      scheduledTime: "desc",
    },
  });
};

// Removido o cache para garantir que sempre obtemos os dados mais recentes
export const getDriverBookingStats = async (driverId?: string) => {
  if (!driverId) {
    console.warn("getDriverBookingStats kalt uten en gyldig driverId");
    return [];
  }

  // Carregar os dados do localStorage se estiver no navegador
  if (typeof window !== "undefined") {
    try {
      console.log(
        "getDriverBookingStats - Loading mock data from localStorage"
      );
      loadMockDataFromStorage();
    } catch (e) {
      console.error("Error loading mock data from localStorage:", e);
    }
  }

  // Bruk mockdata hvis USE_MOCK_DATA er satt til true
  if (useMockData()) {
    console.log("data-fetching.ts - Using mock data for driver booking stats");
    const stats = await mockDataFetching.getDriverBookingStats(driverId);
    console.log(
      `Retrieved ${stats.length} booking stats for driver ${driverId}`
    );
    return stats;
  }

  const bookingsByStatus = await db.booking.groupBy({
    by: ["status"],
    where: {
      driverId: driverId,
    },
    _count: {
      status: true,
    },
  });

  return bookingsByStatus.map((item) => ({
    status: item.status,
    _count: item._count.status,
  }));
};

// Spesifikke funksjoner for kundens dashbord
// Removido o cache para garantir que sempre obtemos os dados mais recentes
export const getCustomerBookings = async (customerId?: string) => {
  if (!customerId) {
    console.warn("getCustomerBookings kalt uten en gyldig customerId");
    return [];
  }

  // Carregar os dados do localStorage se estiver no navegador
  if (typeof window !== "undefined") {
    try {
      console.log("getCustomerBookings - Loading mock data from localStorage");
      loadMockDataFromStorage();
    } catch (e) {
      console.error("Error loading mock data from localStorage:", e);
    }
  }

  // Bruk mockdata hvis USE_MOCK_DATA er satt til true
  if (useMockData()) {
    console.log("data-fetching.ts - Using mock data for customer bookings");

    // Garantir que os dados do motorista estejam atualizados
    if (typeof window !== "undefined") {
      const driverProfileStr = localStorage.getItem("driverProfile");
      if (driverProfileStr) {
        try {
          // Obter o driver profile completo
          const driverProfile = JSON.parse(driverProfileStr);

          // Construir o objeto de dados do driver
          const driverData = {
            name: driverProfile?.name || "",
            phone: driverProfile?.phone || "",
            image: driverProfile?.image || "",
            driverProfile: driverProfile?.driverProfile || {},
          };

          // Atualizar as informações do motorista em todas as corridas
          import("@/lib/mock-data").then(({ updateDriverInfoInBookings }) => {
            updateDriverInfoInBookings("driver1", driverData, true);
            console.log(
              "getCustomerBookings: Updated driver info in all bookings"
            );
          });
        } catch (e) {
          console.error("getCustomerBookings: Error updating driver info:", e);
        }
      }
    }

    const bookings = await mockDataFetching.getCustomerBookings(customerId);
    console.log(
      `Retrieved ${bookings.length} bookings for customer ${customerId}`
    );

    // Verificar se as corridas têm os dados corretos do driver
    if (bookings.length > 0 && bookings[0].driver) {
      const firstBooking = bookings[0];
      console.log(`First booking driver: ${firstBooking.driver.name}`);
      console.log(
        `First booking driver image: ${
          firstBooking.driver.image
            ? firstBooking.driver.image.substring(0, 30) + "..."
            : "None"
        }`
      );
      // @ts-ignore - Verificando propriedade driverProfile
      if (firstBooking.driver.driverProfile) {
        console.log("First booking driver has profile data");
      }
    }

    return bookings;
  }

  return db.booking.findMany({
    where: {
      customerId: customerId,
    },
    include: {
      vehicle: true,
      driver: {
        select: {
          name: true,
          email: true,
          phone: true,
          image: true, // Adicionado para garantir que a imagem do motorista seja incluída
        },
      },
    },
    orderBy: {
      scheduledTime: "desc",
    },
  });
};

// Funksjon for å hente dagens bestillinger
// Removido o cache para garantir que sempre obtemos os dados mais recentes
export const getTodaysBookings = async () => {
  // Carregar os dados do localStorage se estiver no navegador
  if (typeof window !== "undefined") {
    try {
      console.log("getTodaysBookings - Loading mock data from localStorage");
      loadMockDataFromStorage();
    } catch (e) {
      console.error("Error loading mock data from localStorage:", e);
    }
  }

  // Bruk mockdata hvis USE_MOCK_DATA er satt til true
  if (useMockData()) {
    console.log("data-fetching.ts - Using mock data for today's bookings");
    const bookings = await mockDataFetching.getTodaysBookings();
    console.log(`Retrieved ${bookings.length} bookings for today`);

    // Verificar se as corridas têm os dados corretos do driver
    if (bookings.length > 0 && bookings[0].driver) {
      const firstBooking = bookings[0];
      console.log(`First today's booking driver: ${firstBooking.driver.name}`);
      console.log(
        `First today's booking driver image: ${
          firstBooking.driver.image
            ? firstBooking.driver.image.substring(0, 30) + "..."
            : "None"
        }`
      );
      // @ts-ignore - Verificando propriedade driverProfile
      if (firstBooking.driver.driverProfile) {
        console.log("First today's booking driver has profile data");
      }
    }

    return bookings;
  }

  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  return db.booking.findMany({
    where: {
      scheduledTime: {
        gte: today,
        lt: tomorrow,
      },
    },
    include: {
      vehicle: true,
      customer: {
        select: {
          name: true,
          email: true,
          phone: true,
        },
      },
      driver: {
        select: {
          name: true,
          email: true,
          phone: true,
          image: true, // Adicionado para garantir que a imagem do motorista seja incluída
        },
      },
    },
    orderBy: {
      scheduledTime: "asc",
    },
  });
};

// Funksjon for å hente en bestilling etter ID
// Removido o cache para garantir que sempre obtemos os dados mais recentes
export const getBookingById = async (id: string) => {
  // Carregar os dados do localStorage se estiver no navegador
  if (typeof window !== "undefined") {
    try {
      console.log("getBookingById - Loading mock data from localStorage");
      loadMockDataFromStorage();
    } catch (e) {
      console.error("Error loading mock data from localStorage:", e);
    }
  }

  // Bruk mockdata hvis USE_MOCK_DATA er satt til true
  if (useMockData()) {
    console.log("data-fetching.ts - Using mock data for booking by ID");
    const booking = await mockDataFetching.getBookingById(id);

    if (booking && booking.driver) {
      console.log(`Booking ${id} driver: ${booking.driver.name}`);
      console.log(
        `Booking driver image: ${
          booking.driver.image
            ? booking.driver.image.substring(0, 30) + "..."
            : "None"
        }`
      );
      // @ts-ignore - Verificando propriedade driverProfile
      if (booking.driver.driverProfile) {
        console.log("Booking driver has profile data");
        // @ts-ignore - Acessando propriedades do driverProfile
        console.log(
          `Driver license: ${
            booking.driver.driverProfile.licenseNumber || "Not set"
          }`
        );
      }
    }

    return booking;
  }

  return db.booking.findUnique({
    where: {
      id: id,
    },
    include: {
      vehicle: true,
      customer: {
        select: {
          name: true,
          email: true,
          phone: true,
        },
      },
      driver: {
        select: {
          name: true,
          email: true,
          phone: true,
          image: true, // Adicionado para garantir que a imagem do motorista seja incluída
        },
      },
    },
  });
};

// Funksjon for å hente statistikk for kundens bestillinger
// Removido o cache para garantir que sempre obtemos os dados mais recentes
export const getCustomerBookingStats = async (customerId?: string) => {
  if (!customerId) {
    console.warn("getCustomerBookingStats kalt uten en gyldig customerId");
    return {
      totalBookings: 0,
      pendingBookings: 0,
      confirmedBookings: 0,
      completedBookings: 0,
      cancelledBookings: 0,
    };
  }

  // Bruk mockdata hvis USE_MOCK_DATA er satt til true
  if (useMockData()) {
    console.log(
      "data-fetching.ts - Using mock data for customer booking stats"
    );
    return mockDataFetching.getCustomerBookingStats(customerId);
  }

  const [
    totalBookings,
    pendingBookings,
    confirmedBookings,
    completedBookings,
    cancelledBookings,
  ] = await Promise.all([
    db.booking.count({ where: { customerId } }),
    db.booking.count({ where: { customerId, status: "PENDING" } }),
    db.booking.count({ where: { customerId, status: "CONFIRMED" } }),
    db.booking.count({ where: { customerId, status: "COMPLETED" } }),
    db.booking.count({ where: { customerId, status: "CANCELLED" } }),
  ]);

  return {
    totalBookings,
    pendingBookings,
    confirmedBookings,
    completedBookings,
    cancelledBookings,
  };
};
