// Mock data fetching functions for development
import { cache } from "react";
import {
  mockBookings,
  mockUsers,
  mockVehicles,
  mockStats,
  addMockBooking,
} from "@/lib/mock-data";
import { getMockUserById } from "@/auth.config.mock";
import { BookingStatus, UserRole, VehicleType } from "@prisma/client";
import { v4 as uuidv4 } from "uuid";

// Admin dashboard functions
export const getBookings = cache(async (limit?: number) => {
  console.log("getBookings - returning all mock bookings");
  let result = [...mockBookings];
  if (limit) {
    result = result.slice(0, limit);
  }

  // Ordenar por data mais recente
  result.sort(
    (a, b) =>
      new Date(b.scheduledTime).getTime() - new Date(a.scheduledTime).getTime()
  );

  console.log(`Returning ${result.length} bookings`);
  return result;
});

export const getBookingStats = cache(async () => {
  return {
    totalBookings: mockBookings.length,
    pendingBookings: mockBookings.filter(
      (booking) => booking.status === "PENDING"
    ).length,
    confirmedBookings: mockBookings.filter(
      (booking) => booking.status === "CONFIRMED"
    ).length,
    completedBookings: mockBookings.filter(
      (booking) => booking.status === "COMPLETED"
    ).length,
    cancelledBookings: mockBookings.filter(
      (booking) => booking.status === "CANCELLED"
    ).length,
  };
});

export const getDriverStats = cache(async () => {
  const activeDrivers = mockUsers.filter(
    (user) => user.role === "DRIVER"
  ).length;
  return {
    activeDrivers,
    availableDrivers: activeDrivers,
    totalTrips: mockBookings.length,
    averageRating: 4.7,
  };
});

export const getVehicleStats = cache(async () => {
  const availableVehicles = mockVehicles.filter(
    (vehicle) => vehicle.isAvailable
  ).length;
  return {
    totalVehicles: mockVehicles.length,
    availableVehicles,
    inUseVehicles: mockVehicles.length - availableVehicles,
    maintenanceNeeded: 1,
  };
});

// Removido o cache para garantir que sempre obtemos os dados mais recentes
export const getDrivers = async () => {
  console.log("getDrivers - returning all drivers without cache");
  return mockUsers.filter((user) => user.role === "DRIVER");
};

// Removido o cache para garantir que sempre obtemos os dados mais recentes
export const getUsers = async () => {
  console.log("getUsers - returning all users without cache");
  return mockUsers.map(({ passwordHash, ...user }) => user);
};

export const getVehicles = cache(async () => {
  return mockVehicles;
});

// Driver dashboard functions
// Função para gerar dados mockados para um novo usuário
function generateMockDataForUser(userId: string) {
  // Verificar se o usuário existe
  const user = getMockUserById(userId);

  // Verificar se é uma conta de exemplo pelo ID ou email
  // Primeiro, verificamos se o ID corresponde diretamente a um dos IDs de exemplo
  if (userId === "driver1" || userId === "customer1" || userId === "admin1") {
    console.log(`Example user detected by ID (${userId}), returning mock data`);

    // Para o motorista de exemplo, retornar todas as corridas com driverId = "driver1"
    if (userId === "driver1") {
      const driverBookings = mockBookings.filter(
        (booking) => booking.driverId === "driver1"
      );
      console.log(`Found ${driverBookings.length} bookings for driver1`);
      return driverBookings;
    }

    // Para o cliente de exemplo, retornar todas as corridas com customerId = "customer1"
    if (userId === "customer1") {
      return mockBookings.filter(
        (booking) => booking.customerId === "customer1"
      );
    }

    // Para o admin de exemplo, retornar todas as corridas
    if (userId === "admin1") {
      console.log("Admin account detected, returning all mock bookings");
      return mockBookings;
    }
  }

  // Se não encontramos pelo ID, verificamos pelo usuário encontrado
  if (user) {
    console.log("User found:", user);

    // Verificar se é uma conta de exemplo pelo email
    const isExampleUser =
      user.email === "<EMAIL>" ||
      user.email === "<EMAIL>" ||
      user.email === "<EMAIL>";

    if (isExampleUser) {
      console.log("Example user detected by email, returning mock data");

      // Para o cliente de exemplo, retornar todas as corridas com customerId = "customer1"
      if (user.email === "<EMAIL>") {
        return mockBookings.filter(
          (booking) => booking.customerId === "customer1"
        );
      }

      // Para o motorista de exemplo, retornar todas as corridas com driverId = "driver1"
      if (user.email === "<EMAIL>") {
        const driverBookings = mockBookings.filter(
          (booking) => booking.driverId === "driver1"
        );
        console.log(
          `Found ${driverBookings.length} <NAME_EMAIL>`
        );
        return driverBookings;
      }

      // Para o admin de exemplo, retornar todas as corridas
      if (user.email === "<EMAIL>") {
        console.log("Admin account detected, returning all mock bookings");
        return mockBookings;
      }
    }
  }

  // Verificação adicional para o driver de exemplo
  // Isso é um fallback para garantir que o driver de exemplo sempre veja suas corridas
  if (
    userId &&
    mockBookings.some(
      (booking) => booking.driver && booking.driver.id === userId
    )
  ) {
    console.log(
      `User ID ${userId} found in bookings as driver, returning related bookings`
    );
    return mockBookings.filter(
      (booking) => booking.driver && booking.driver.id === userId
    );
  }

  // Verificação especial para o driver de exemplo pelo email
  // Se o ID não for exatamente "driver1", mas contiver "driver" ou for um UUID gerado para o driver de exemplo
  if (userId && (userId.includes("driver") || userId.length > 10)) {
    console.log(
      `User ID ${userId} parece ser um driver, verificando se é o driver de exemplo`
    );

    // Verificar se há corridas para o driver1 nos dados mockados
    const driverBookings = mockBookings.filter(
      (booking) => booking.driverId === "driver1"
    );
    if (driverBookings.length > 0) {
      console.log(
        `Encontradas ${driverBookings.length} corridas para o driver de exemplo, retornando`
      );
      return driverBookings;
    }
  }

  // Para novos usuários, não gerar dados mockados
  console.log(
    `Nenhum dado mockado para o usuário ${userId} - usuário não é uma conta de exemplo`
  );
  return [];
}

export const getDriverBookings = cache(async (driverId?: string) => {
  if (!driverId) {
    console.warn("getDriverBookings kalt uten en gyldig driverId");
    return [];
  }

  console.log("getDriverBookings - driverId:", driverId);

  // Verificar se o ID corresponde ao driver de exemplo
  const isExampleDriver = driverId === "driver1";
  console.log("getDriverBookings - isExampleDriver:", isExampleDriver);

  // Verificar se há corridas para este motorista nos dados mockados
  const driverBookingsInMock = mockBookings.filter(
    (booking) => booking.driverId === "driver1"
  );
  console.log(
    "getDriverBookings - driverBookingsInMock count:",
    driverBookingsInMock.length
  );

  // Usar a função generateMockDataForUser que agora retorna dados apenas para contas de exemplo
  const result = generateMockDataForUser(driverId);
  console.log("getDriverBookings - result count:", result.length);

  // Se não encontramos resultados, mas sabemos que é o driver de exemplo, forçar o retorno das corridas
  if (
    result.length === 0 &&
    (driverId === "driver1" ||
      driverId.includes("driver") ||
      driverId.length > 10)
  ) {
    console.log(
      "getDriverBookings - Forçando retorno de corridas para o driver de exemplo"
    );
    return driverBookingsInMock;
  }

  // Se ainda não temos resultados, mas o email do usuário é <EMAIL>, forçar o retorno das corridas
  if (result.length === 0) {
    console.log(
      "getDriverBookings - Última tentativa de encontrar corridas para o driver de exemplo"
    );
    return mockBookings.filter((booking) => booking.driverId === "driver1");
  }

  return result;
});

export const getDriverBookingStats = cache(async (driverId?: string) => {
  if (!driverId) {
    console.warn("getDriverBookingStats kalt uten en gyldig driverId");
    return [];
  }

  console.log("getDriverBookingStats - driverId:", driverId);

  // Verificar se o ID corresponde ao driver de exemplo
  const isExampleDriver = driverId === "driver1";
  console.log("getDriverBookingStats - isExampleDriver:", isExampleDriver);

  // Obter as reservas do motorista (apenas dados mockados para contas de exemplo)
  let driverBookings = generateMockDataForUser(driverId);

  // Se não encontramos resultados, mas sabemos que é o driver de exemplo, forçar o uso das corridas do driver1
  if (
    driverBookings.length === 0 &&
    (driverId === "driver1" ||
      driverId.includes("driver") ||
      driverId.length > 10)
  ) {
    console.log(
      "getDriverBookingStats - Forçando uso de corridas para o driver de exemplo"
    );
    driverBookings = mockBookings.filter(
      (booking) => booking.driverId === "driver1"
    );
    console.log(
      `getDriverBookingStats - Encontradas ${driverBookings.length} corridas para o driver de exemplo`
    );
  }

  console.log(
    "getDriverBookingStats - driverBookings count:",
    driverBookings.length
  );

  // Group bookings by status
  const statuses = [
    "PENDING",
    "CONFIRMED",
    "IN_PROGRESS",
    "COMPLETED",
    "CANCELLED",
  ];
  return statuses.map((status) => {
    const count = driverBookings.filter(
      (booking) => booking.status === status
    ).length;
    return {
      status,
      _count: count,
    };
  });
});

// Customer dashboard functions
// Removido o cache para garantir que sempre obtemos os dados mais recentes
export const getCustomerBookings = async (customerId?: string) => {
  if (!customerId) {
    console.warn("getCustomerBookings kalt uten en gyldig customerId");
    return [];
  }

  console.log("getCustomerBookings - customerId:", customerId);

  // Sincronizar os dados do driver para garantir que as corridas tenham as informações mais recentes
  if (typeof window !== "undefined") {
    try {
      // @ts-ignore - Acessando função global
      if (window.syncDriverData) {
        console.log("getCustomerBookings - Syncing driver data...");
        // @ts-ignore - Acessando função global
        window.syncDriverData();
      }
    } catch (e) {
      console.error("Error syncing driver data:", e);
    }
  }

  // Verificar se é uma conta de exemplo pelo ID da sessão
  // Isso é um fallback para quando o ID da sessão não corresponde a um usuário mockado
  const user = getMockUserById(customerId);
  if (!user) {
    console.log(
      "User not found in mock data, checking if it's the example customer account"
    );

    // Se estamos usando dados mockados e o usuário não foi encontrado,
    // assumimos que é a conta de exemplo do cliente
    if (process.env.USE_MOCK_DATA === "true") {
      console.log("Returning mock data for example customer account");
      // Retornar todas as corridas com customerId = "customer1"
      const exampleBookings = mockBookings.filter(
        (booking) => booking.customerId === "customer1"
      );
      console.log(
        `Found ${exampleBookings.length} bookings for example customer`
      );
      return exampleBookings;
    }

    return [];
  }

  // Verificar se é a conta de exemplo do cliente pelo email
  if (user.email === "<EMAIL>") {
    console.log("Example customer account detected, returning fresh mock data");
    // Retornar todas as corridas com customerId = "customer1"
    const customerBookings = mockBookings.filter(
      (booking) => booking.customerId === "customer1"
    );
    console.log(
      `Found ${customerBookings.length} bookings for example customer`
    );
    return customerBookings;
  }

  // Usar a função generateMockDataForUser que agora retorna dados apenas para contas de exemplo
  const result = generateMockDataForUser(customerId);
  console.log("getCustomerBookings - result count:", result.length);

  return result;
};

// Removido o cache para garantir que sempre obtemos os dados mais recentes
export const getCustomerBookingStats = async (customerId?: string) => {
  if (!customerId) {
    console.warn("getCustomerBookingStats kalt uten en gyldig customerId");
    return {
      totalBookings: 0,
      pendingBookings: 0,
      confirmedBookings: 0,
      completedBookings: 0,
      cancelledBookings: 0,
    };
  }

  // Obter as reservas do cliente diretamente dos dados mockados para garantir dados atualizados
  let customerBookings;

  // Verificar se é a conta de exemplo do cliente
  const user = getMockUserById(customerId);
  if (user && user.email === "<EMAIL>") {
    // Obter todas as corridas do cliente de exemplo diretamente
    customerBookings = mockBookings.filter(
      (booking) => booking.customerId === "customer1"
    );
    console.log(
      `Found ${customerBookings.length} bookings for example customer stats`
    );
  } else {
    // Para outros usuários, usar a função generateMockDataForUser
    customerBookings = generateMockDataForUser(customerId);
  }

  return {
    totalBookings: customerBookings.length,
    pendingBookings: customerBookings.filter(
      (booking) => booking.status === "PENDING"
    ).length,
    confirmedBookings: customerBookings.filter(
      (booking) => booking.status === "CONFIRMED"
    ).length,
    completedBookings: customerBookings.filter(
      (booking) => booking.status === "COMPLETED"
    ).length,
    cancelledBookings: customerBookings.filter(
      (booking) => booking.status === "CANCELLED"
    ).length,
  };
};

// Shared functions
// Removido o cache para garantir que sempre obtemos os dados mais recentes
export const getTodaysBookings = async () => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  const todayBookings = mockBookings.filter((booking) => {
    const bookingDate = new Date(booking.scheduledTime);
    return bookingDate >= today && bookingDate < tomorrow;
  });

  console.log(`Found ${todayBookings.length} bookings for today`);
  return todayBookings;
};

// Removido o cache para garantir que sempre obtemos os dados mais recentes
export const getBookingById = async (id: string) => {
  console.log(`Getting booking by ID: ${id}`);

  // Sincronizar os dados do driver para garantir que as corridas tenham as informações mais recentes
  if (typeof window !== "undefined") {
    try {
      // @ts-ignore - Acessando função global
      if (window.syncDriverData) {
        console.log("getBookingById - Syncing driver data...");
        // @ts-ignore - Acessando função global
        window.syncDriverData();
      }
    } catch (e) {
      console.error("Error syncing driver data:", e);
    }
  }

  const booking = mockBookings.find((booking) => booking.id === id) || null;
  if (booking) {
    console.log(
      `Found booking: ${booking.id}, driver: ${
        booking.driver?.name
      }, image: ${booking.driver?.image?.substring(0, 30)}...`
    );
  }
  return booking;
};

// Função para atualizar o perfil do motorista nos dados mockados
export const updateDriverProfile = async (
  driverId: string,
  profileData: any
) => {
  console.log("updateDriverProfile - driverId:", driverId);
  console.log("updateDriverProfile - profileData:", profileData);

  try {
    // Verificar se o motorista existe nos dados mockados
    const driverIndex = mockUsers.findIndex(
      (user) =>
        user.id === driverId ||
        (user.role === "DRIVER" && user.email === "<EMAIL>")
    );

    if (driverIndex === -1) {
      console.log("Driver not found in mock data");
      return { success: false, error: "Driver not found" };
    }

    // Atualizar os dados do motorista
    const updatedDriver = {
      ...mockUsers[driverIndex],
      name: profileData.name || mockUsers[driverIndex].name,
      phone: profileData.phone || mockUsers[driverIndex].phone,
      image: profileData.image || mockUsers[driverIndex].image,
      // Não atualizamos o email, pois ele é único
      // Adicionar campos adicionais do perfil como propriedades do usuário
      driverProfile: {
        licenseNumber: profileData.licenseNumber || "",
        licenseType: profileData.licenseType || "",
        experienceYears: profileData.experienceYears
          ? parseInt(profileData.experienceYears)
          : 0,
        preferredVehicleType: profileData.preferredVehicleType || "",
        availableWeekends: profileData.availableWeekends === true,
        availableNights: profileData.availableNights === true,
        emergencyContactName: profileData.emergencyContactName || "",
        emergencyContactPhone: profileData.emergencyContactPhone || "",
        bio: profileData.bio || "",
        address: profileData.address || "",
        postalCode: profileData.postalCode || "",
        city: profileData.city || "",
      },
    };

    // Atualizar o motorista nos dados mockados
    mockUsers[driverIndex] = updatedDriver;
    console.log("Updated mock user with all profile data:", updatedDriver.name);
    console.log("Driver profile data:", updatedDriver.driverProfile);

    // Salvar os dados do motorista no localStorage (se estiver no navegador)
    if (typeof window !== "undefined") {
      try {
        // Criar um objeto com todos os dados do perfil
        const profileToSave = {
          id: updatedDriver.id,
          name: updatedDriver.name,
          phone: updatedDriver.phone,
          image: profileData.image || updatedDriver.image, // Usar a imagem dos dados recebidos, não do driver atualizado
          email: updatedDriver.email,
          // Adicionar outros campos do perfil como um objeto driverProfile
          driverProfile: updatedDriver.driverProfile,
        };

        // Salvar os dados no localStorage
        localStorage.setItem("driverProfile", JSON.stringify(profileToSave));

        // Salvar também em itens separados para garantir persistência
        localStorage.setItem(
          "driverProfileImage",
          profileData.image || updatedDriver.image || ""
        );

        // Salvar cada campo individualmente para garantir persistência
        localStorage.setItem("driverName", updatedDriver.name);
        localStorage.setItem("driverPhone", updatedDriver.phone || "");
        localStorage.setItem(
          "driverLicenseNumber",
          profileData.licenseNumber || ""
        );
        localStorage.setItem(
          "driverLicenseType",
          profileData.licenseType || ""
        );
        localStorage.setItem("driverAddress", profileData.address || "");
        localStorage.setItem("driverCity", profileData.city || "");
        localStorage.setItem("driverBio", profileData.bio || "");

        console.log("Driver profile saved to localStorage with all data");
        console.log(
          "Driver image:",
          (profileData.image || updatedDriver.image || "").substring(0, 30) +
            "..."
        );
      } catch (e) {
        console.error("Error saving to localStorage:", e);
      }
    }
    // Atualizar as informações do motorista em todas as corridas
    // Isso garante que as corridas exibam as informações atualizadas do motorista
    console.log("Updating driver info in all bookings");

    // Garantir que estamos atualizando o driver correto
    const targetDriverId = "driver1"; // Sempre usar driver1 para o driver de exemplo

    // Atualizar todas as corridas que têm este motorista
    let updatedCount = 0;
    mockBookings.forEach((booking) => {
      if (booking.driverId === targetDriverId && booking.driver) {
        // Atualizar todas as propriedades do motorista
        booking.driver.name = updatedDriver.name;
        booking.driver.phone = updatedDriver.phone || "";
        if (profileData.image) {
          booking.driver.image = profileData.image;
        }

        // Adicionar campos adicionais do perfil ao driver nas corridas
        // @ts-ignore - Adicionando propriedade driverProfile ao objeto driver
        booking.driver.driverProfile = updatedDriver.driverProfile;

        updatedCount++;
      }
    });

    console.log(`Updated driver info in ${updatedCount} bookings`);

    // Verificar se a atualização foi bem-sucedida
    const verifyBooking = mockBookings.find(
      (b) => b.driverId === targetDriverId
    );
    if (verifyBooking && verifyBooking.driver) {
      console.log(
        `Verification - Booking driver name: ${verifyBooking.driver.name}`
      );
      console.log(
        `Verification - Booking driver phone: ${verifyBooking.driver.phone}`
      );
      console.log(
        `Verification - Booking driver image: ${verifyBooking.driver.image?.substring(
          0,
          30
        )}...`
      );
      // @ts-ignore - Verificando propriedade driverProfile
      if (verifyBooking.driver.driverProfile) {
        console.log(`Verification - Booking driver profile data exists`);
        // @ts-ignore - Acessando propriedades do driverProfile
        console.log(
          `Verification - Driver license: ${verifyBooking.driver.driverProfile.licenseNumber}`
        );
      }
    }

    console.log("Driver profile updated successfully");
    return { success: true, driver: updatedDriver };
  } catch (error) {
    console.error("Error updating driver profile:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
};
