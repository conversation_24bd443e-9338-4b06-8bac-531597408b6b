// Testdata for utvikling når databasen ikke er tilgjengelig
import { BookingStatus, UserRole, VehicleType } from "@prisma/client";
import { v4 as uuidv4 } from "uuid";

// Função para adicionar uma nova reserva mockada
export function addMockBooking(booking: any) {
  mockBookings.push(booking);
  return booking;
}

// Função para criar uma data relativa ao dia atual
function createRelativeDate(
  dayOffset: number,
  hour: number = 12,
  minute: number = 0
) {
  const date = new Date();
  date.setDate(date.getDate() + dayOffset);
  date.setHours(hour, minute, 0, 0);
  return date;
}

// Função para criar uma data relativa ao dia atual com hora aleatória
function createRandomTimeRelativeDate(dayOffset: number) {
  const date = new Date();
  date.setDate(date.getDate() + dayOffset);
  // Hora aleatória entre 8 e 20
  const randomHour = Math.floor(Math.random() * 12) + 8;
  // Minuto aleatório (0, 15, 30, 45)
  const randomMinute = Math.floor(Math.random() * 4) * 15;
  date.setHours(randomHour, randomMinute, 0, 0);
  return date;
}

export const mockBookings = [
  {
    id: "1",
    customerId: "customer1",
    driverId: "driver1",
    vehicleId: "v1",
    status: "CONFIRMED" as BookingStatus,
    pickupLocation: "Oslo Sentrum",
    dropoffLocation: "Gardermoen Flyplass",
    scheduledTime: new Date(2023, 5, 15, 10, 0),
    passengers: 2,
    specialRequests: "Extra luggage",
    createdAt: new Date(2023, 5, 10),
    updatedAt: new Date(2023, 5, 10),
    customer: {
      id: "customer1",
      email: "<EMAIL>",
      name: "John Doe",
      phone: "+47 123 45 678",
      role: "CUSTOMER" as UserRole,
    },
    driver: {
      id: "driver1",
      email: "<EMAIL>",
      name: "Test 1 Hansen",
      phone: "+47 99452211",
      role: "DRIVER" as UserRole,
      image:
        "http://localhost:9000/viken-tours/profile-images/1746598119314-italo1.png",
    },
    vehicle: {
      id: "v1",
      model: "Tesla Model Y",
      plateNumber: "EL12345",
      type: "VIP_SEDAN" as VehicleType,
      capacity: 4,
      isAvailable: true,
    },
    payment: {
      id: "p1",
      amount: 1200,
      method: "CREDIT_CARD",
      status: "COMPLETED",
    },
  },
  {
    id: "2",
    customerId: "customer1",
    driverId: "driver1",
    vehicleId: "v2",
    status: "PENDING" as BookingStatus,
    pickupLocation: "Bergen Sentrum",
    dropoffLocation: "Bergen Flyplass",
    scheduledTime: new Date(2023, 5, 16, 14, 30),
    passengers: 5,
    specialRequests: null,
    createdAt: new Date(2023, 5, 12),
    updatedAt: new Date(2023, 5, 12),
    customer: {
      id: "customer1",
      email: "<EMAIL>",
      name: "John Doe",
      phone: "+47 234 56 789",
      role: "CUSTOMER" as UserRole,
    },
    driver: {
      id: "driver1",
      email: "<EMAIL>",
      name: "Test 1 Hansen",
      phone: "+47 99452211",
      role: "DRIVER" as UserRole,
      image:
        "http://localhost:9000/viken-tours/profile-images/1746598119314-italo1.png",
    },
    vehicle: {
      id: "v2",
      model: "Mercedes Sprinter",
      plateNumber: "AB67890",
      type: "MINIBUS" as VehicleType,
      capacity: 8,
      isAvailable: true,
    },
    payment: null,
  },
  {
    id: "3",
    customerId: "customer1",
    driverId: null,
    vehicleId: "v3",
    status: "PENDING" as BookingStatus,
    pickupLocation: "Trondheim Sentrum",
    dropoffLocation: "Værnes Flyplass",
    scheduledTime: new Date(2023, 5, 17, 9, 15),
    passengers: 12,
    specialRequests: "Group booking",
    createdAt: new Date(2023, 5, 13),
    updatedAt: new Date(2023, 5, 13),
    customer: {
      id: "customer1",
      email: "<EMAIL>",
      name: "John Doe",
      phone: "+47 345 67 890",
      role: "CUSTOMER" as UserRole,
    },
    driver: null,
    vehicle: {
      id: "v3",
      model: "Volvo Coach",
      plateNumber: "CD12345",
      type: "BUS" as VehicleType,
      capacity: 16,
      isAvailable: true,
    },
    payment: null,
  },
  {
    id: "4",
    customerId: "customer1",
    driverId: "driver1",
    vehicleId: "v1",
    status: "COMPLETED" as BookingStatus,
    pickupLocation: "Oslo Sentrum",
    dropoffLocation: "Oslo Vest",
    scheduledTime: new Date(2023, 5, 10, 16, 45),
    passengers: 2,
    specialRequests: null,
    createdAt: new Date(2023, 5, 8),
    updatedAt: new Date(2023, 5, 10),
    customer: {
      id: "customer1",
      email: "<EMAIL>",
      name: "John Doe",
      phone: "+47 123 45 678",
      role: "CUSTOMER" as UserRole,
    },
    driver: {
      id: "driver1",
      email: "<EMAIL>",
      name: "Test 1 Hansen",
      phone: "+47 99452211",
      role: "DRIVER" as UserRole,
      image:
        "http://localhost:9000/viken-tours/profile-images/1746598119314-italo1.png",
    },
    vehicle: {
      id: "v1",
      model: "Tesla Model Y",
      plateNumber: "EL12345",
      type: "VIP_SEDAN" as VehicleType,
      capacity: 4,
      isAvailable: true,
    },
    payment: {
      id: "p2",
      amount: 800,
      method: "CREDIT_CARD",
      status: "COMPLETED",
    },
  },
  {
    id: "5",
    customerId: "customer1",
    driverId: "driver1",
    vehicleId: "v2",
    status: "CANCELLED" as BookingStatus,
    pickupLocation: "Stavanger Sentrum",
    dropoffLocation: "Sola Flyplass",
    scheduledTime: new Date(2023, 5, 11, 8, 0),
    passengers: 4,
    specialRequests: null,
    createdAt: new Date(2023, 5, 9),
    updatedAt: new Date(2023, 5, 10),
    customer: {
      id: "customer1",
      email: "<EMAIL>",
      name: "John Doe",
      phone: "+47 234 56 789",
      role: "CUSTOMER" as UserRole,
    },
    driver: {
      id: "driver1",
      email: "<EMAIL>",
      name: "Test 1 Hansen",
      phone: "+47 99452211",
      role: "DRIVER" as UserRole,
      image:
        "http://localhost:9000/viken-tours/profile-images/1746598119314-italo1.png",
    },
    vehicle: {
      id: "v2",
      model: "Mercedes Sprinter",
      plateNumber: "AB67890",
      type: "MINIBUS" as VehicleType,
      capacity: 8,
      isAvailable: true,
    },
    payment: {
      id: "p3",
      amount: 950,
      method: "CREDIT_CARD",
      status: "REFUNDED",
    },
  },
  // Legg til flere bestillinger med datoer nærmere gjeldende dato
  {
    id: "6",
    customerId: "customer1",
    driverId: "driver1",
    vehicleId: "v1",
    status: "CONFIRMED" as BookingStatus,
    pickupLocation: "Oslo Sentrum",
    dropoffLocation: "Gardermoen Flyplass",
    scheduledTime: new Date(new Date().getTime() + 24 * 60 * 60 * 1000), // I morgen
    passengers: 2,
    specialRequests: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    customer: {
      id: "customer1",
      email: "<EMAIL>",
      name: "John Doe",
      phone: "+47 123 45 678",
      role: "CUSTOMER" as UserRole,
    },
    driver: {
      id: "driver1",
      email: "<EMAIL>",
      name: "Test 1 Hansen",
      phone: "+47 99452211",
      role: "DRIVER" as UserRole,
      image:
        "http://localhost:9000/viken-tours/profile-images/1746598119314-italo1.png",
    },
    vehicle: {
      id: "v1",
      model: "Tesla Model Y",
      plateNumber: "EL12345",
      type: "VIP_SEDAN" as VehicleType,
      capacity: 4,
      isAvailable: true,
    },
    payment: null,
  },
  {
    id: "7",
    customerId: "customer1",
    driverId: "driver1",
    vehicleId: "v2",
    status: "PENDING" as BookingStatus,
    pickupLocation: "Bergen Sentrum",
    dropoffLocation: "Bergen Flyplass",
    scheduledTime: new Date(new Date().getTime() + 2 * 24 * 60 * 60 * 1000), // I overmorgen
    passengers: 6,
    specialRequests: "Family with children",
    createdAt: new Date(),
    updatedAt: new Date(),
    customer: {
      id: "customer1",
      email: "<EMAIL>",
      name: "John Doe",
      phone: "+47 234 56 789",
      role: "CUSTOMER" as UserRole,
    },
    driver: {
      id: "driver1",
      email: "<EMAIL>",
      name: "Test 1 Hansen",
      phone: "+47 99452211",
      role: "DRIVER" as UserRole,
      image:
        "http://localhost:9000/viken-tours/profile-images/1746598119314-italo1.png",
    },
    vehicle: {
      id: "v2",
      model: "Mercedes Sprinter",
      plateNumber: "AB67890",
      type: "MINIBUS" as VehicleType,
      capacity: 8,
      isAvailable: true,
    },
    payment: null,
  },
  {
    id: "8",
    customerId: "customer1",
    driverId: "driver1",
    vehicleId: "v3",
    status: "CONFIRMED" as BookingStatus,
    pickupLocation: "Trondheim Sentrum",
    dropoffLocation: "Værnes Flyplass",
    scheduledTime: new Date(), // I dag
    passengers: 10,
    specialRequests: null,
    createdAt: new Date(new Date().getTime() - 24 * 60 * 60 * 1000),
    updatedAt: new Date(),
    customer: {
      id: "customer1",
      email: "<EMAIL>",
      name: "John Doe",
      phone: "+47 345 67 890",
      role: "CUSTOMER" as UserRole,
    },
    driver: {
      id: "driver1",
      email: "<EMAIL>",
      name: "Test 1 Hansen",
      phone: "+47 99452211",
      role: "DRIVER" as UserRole,
      image:
        "http://localhost:9000/viken-tours/profile-images/1746598119314-italo1.png",
    },
    vehicle: {
      id: "v3",
      model: "Volvo Coach",
      plateNumber: "CD12345",
      type: "BUS" as VehicleType,
      capacity: 16,
      isAvailable: true,
    },
    payment: null,
  },
  // Adicionando mais reservas para o driver e o customer
  {
    id: "9",
    customerId: "customer1",
    driverId: "driver1",
    vehicleId: "v1",
    status: "PENDING" as BookingStatus,
    pickupLocation: "Oslo Sentrum",
    dropoffLocation: "Oslo Vest",
    scheduledTime: new Date(new Date().getTime() + 3 * 24 * 60 * 60 * 1000), // Em 3 dias
    passengers: 2,
    specialRequests: "Business meeting",
    createdAt: new Date(),
    updatedAt: new Date(),
    customer: {
      id: "customer1",
      email: "<EMAIL>",
      name: "John Doe",
      phone: "+47 123 45 678",
      role: "CUSTOMER" as UserRole,
    },
    driver: {
      id: "driver1",
      email: "<EMAIL>",
      name: "Test 1 Hansen",
      phone: "+47 99452211",
      role: "DRIVER" as UserRole,
      image:
        "http://localhost:9000/viken-tours/profile-images/1746598119314-italo1.png",
    },
    vehicle: {
      id: "v1",
      model: "Tesla Model Y",
      plateNumber: "EL12345",
      type: "VIP_SEDAN" as VehicleType,
      capacity: 4,
      isAvailable: true,
    },
    payment: null,
  },
  {
    id: "10",
    customerId: "customer1",
    driverId: "driver1",
    vehicleId: "v2",
    status: "COMPLETED" as BookingStatus,
    pickupLocation: "Bergen Sentrum",
    dropoffLocation: "Bergen Flyplass",
    scheduledTime: new Date(new Date().getTime() - 2 * 24 * 60 * 60 * 1000), // 2 dias atrás
    passengers: 4,
    specialRequests: null,
    createdAt: new Date(new Date().getTime() - 5 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(new Date().getTime() - 2 * 24 * 60 * 60 * 1000),
    customer: {
      id: "customer1",
      email: "<EMAIL>",
      name: "John Doe",
      phone: "+47 234 56 789",
      role: "CUSTOMER" as UserRole,
    },
    driver: {
      id: "driver1",
      email: "<EMAIL>",
      name: "Test 1 Hansen",
      phone: "+47 99452211",
      role: "DRIVER" as UserRole,
      image:
        "http://localhost:9000/viken-tours/profile-images/1746598119314-italo1.png",
    },
    vehicle: {
      id: "v2",
      model: "Mercedes Sprinter",
      plateNumber: "AB67890",
      type: "MINIBUS" as VehicleType,
      capacity: 8,
      isAvailable: true,
    },
    payment: {
      id: "p4",
      amount: 1200,
      method: "CREDIT_CARD",
      status: "COMPLETED",
    },
  },
  // Adicionando mais reservas para o driver
  {
    id: "11",
    customerId: "customer1",
    driverId: "driver1",
    vehicleId: "v1",
    status: "IN_PROGRESS" as BookingStatus,
    pickupLocation: "Oslo Sentrum",
    dropoffLocation: "Oslo Vest",
    scheduledTime: new Date(), // Hoje
    passengers: 3,
    specialRequests: "Business meeting in progress",
    createdAt: new Date(new Date().getTime() - 1 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(),
    customer: {
      id: "customer1",
      email: "<EMAIL>",
      name: "John Doe",
      phone: "+47 123 45 678",
      role: "CUSTOMER" as UserRole,
    },
    driver: {
      id: "driver1",
      email: "<EMAIL>",
      name: "Test 1 Hansen",
      phone: "+47 99452211",
      role: "DRIVER" as UserRole,
      image:
        "http://localhost:9000/viken-tours/profile-images/1746598119314-italo1.png",
    },
    vehicle: {
      id: "v1",
      model: "Tesla Model Y",
      plateNumber: "EL12345",
      type: "VIP_SEDAN" as VehicleType,
      capacity: 4,
      isAvailable: true,
    },
    payment: null,
  },
  // Novas reservas para o cliente de exemplo com datas relativas
  {
    id: "13",
    customerId: "customer1",
    driverId: "driver1",
    vehicleId: "v1",
    status: "CONFIRMED" as BookingStatus,
    pickupLocation: "Majorstuen, Oslo",
    dropoffLocation: "Gardermoen Flyplass",
    scheduledTime: createRelativeDate(1, 9, 30), // Amanhã às 9:30
    passengers: 2,
    specialRequests: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    customer: {
      id: "customer1",
      email: "<EMAIL>",
      name: "John Doe",
      phone: "+47 123 45 678",
      role: "CUSTOMER" as UserRole,
    },
    driver: {
      id: "driver1",
      email: "<EMAIL>",
      name: "Test 1 Hansen",
      phone: "+47 99452211",
      role: "DRIVER" as UserRole,
      image:
        "http://localhost:9000/viken-tours/profile-images/1746598119314-italo1.png",
    },
    vehicle: {
      id: "v1",
      model: "Tesla Model Y",
      plateNumber: "EL12345",
      type: "VIP_SEDAN" as VehicleType,
      capacity: 4,
      isAvailable: true,
    },
    payment: null,
  },
  {
    id: "14",
    customerId: "customer1",
    driverId: "driver1",
    vehicleId: "v2",
    status: "CONFIRMED" as BookingStatus,
    pickupLocation: "Aker Brygge, Oslo",
    dropoffLocation: "Holmenkollen",
    scheduledTime: createRelativeDate(3, 14, 0), // Em 3 dias às 14:00
    passengers: 5,
    specialRequests: "Family trip",
    createdAt: new Date(),
    updatedAt: new Date(),
    customer: {
      id: "customer1",
      email: "<EMAIL>",
      name: "John Doe",
      phone: "+47 123 45 678",
      role: "CUSTOMER" as UserRole,
    },
    driver: {
      id: "driver1",
      email: "<EMAIL>",
      name: "Test 1 Hansen",
      phone: "+47 99452211",
      role: "DRIVER" as UserRole,
      image:
        "http://localhost:9000/viken-tours/profile-images/1746598119314-italo1.png",
    },
    vehicle: {
      id: "v2",
      model: "Mercedes Sprinter",
      plateNumber: "AB67890",
      type: "MINIBUS" as VehicleType,
      capacity: 8,
      isAvailable: true,
    },
    payment: null,
  },
  {
    id: "15",
    customerId: "customer1",
    driverId: "driver1",
    vehicleId: "v1",
    status: "COMPLETED" as BookingStatus,
    pickupLocation: "Grünerløkka, Oslo",
    dropoffLocation: "Oslo S",
    scheduledTime: createRelativeDate(-1, 10, 15), // Ontem às 10:15
    passengers: 1,
    specialRequests: null,
    createdAt: new Date(new Date().getTime() - 3 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(new Date().getTime() - 1 * 24 * 60 * 60 * 1000),
    customer: {
      id: "customer1",
      email: "<EMAIL>",
      name: "John Doe",
      phone: "+47 123 45 678",
      role: "CUSTOMER" as UserRole,
    },
    driver: {
      id: "driver1",
      email: "<EMAIL>",
      name: "Test 1 Hansen",
      phone: "+47 99452211",
      role: "DRIVER" as UserRole,
      image:
        "http://localhost:9000/viken-tours/profile-images/1746598119314-italo1.png",
    },
    vehicle: {
      id: "v1",
      model: "Tesla Model Y",
      plateNumber: "EL12345",
      type: "VIP_SEDAN" as VehicleType,
      capacity: 4,
      isAvailable: true,
    },
    payment: {
      id: "p6",
      amount: 450,
      method: "CREDIT_CARD",
      status: "COMPLETED",
    },
  },
  {
    id: "16",
    customerId: "customer1",
    driverId: "driver1",
    vehicleId: "v1",
    status: "CANCELLED" as BookingStatus,
    pickupLocation: "Frogner, Oslo",
    dropoffLocation: "Bislett, Oslo",
    scheduledTime: createRelativeDate(-2, 16, 30), // 2 dias atrás às 16:30
    passengers: 2,
    specialRequests: null,
    createdAt: new Date(new Date().getTime() - 5 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(new Date().getTime() - 2 * 24 * 60 * 60 * 1000),
    customer: {
      id: "customer1",
      email: "<EMAIL>",
      name: "John Doe",
      phone: "+47 123 45 678",
      role: "CUSTOMER" as UserRole,
    },
    driver: {
      id: "driver1",
      email: "<EMAIL>",
      name: "Test 1 Hansen",
      phone: "+47 99452211",
      role: "DRIVER" as UserRole,
      image:
        "http://localhost:9000/viken-tours/profile-images/1746598119314-italo1.png",
    },
    vehicle: {
      id: "v1",
      model: "Tesla Model Y",
      plateNumber: "EL12345",
      type: "VIP_SEDAN" as VehicleType,
      capacity: 4,
      isAvailable: true,
    },
    payment: null,
  },
  {
    id: "12",
    customerId: "customer1",
    driverId: "driver1",
    vehicleId: "v3",
    status: "COMPLETED" as BookingStatus,
    pickupLocation: "Trondheim Sentrum",
    dropoffLocation: "Værnes Flyplass",
    scheduledTime: new Date(new Date().getTime() - 3 * 24 * 60 * 60 * 1000), // 3 dias atrás
    passengers: 12,
    specialRequests: null,
    createdAt: new Date(new Date().getTime() - 5 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(new Date().getTime() - 3 * 24 * 60 * 60 * 1000),
    customer: {
      id: "customer1",
      email: "<EMAIL>",
      name: "John Doe",
      phone: "+47 345 67 890",
      role: "CUSTOMER" as UserRole,
    },
    driver: {
      id: "driver1",
      email: "<EMAIL>",
      name: "Test 1 Hansen",
      phone: "+47 99452211",
      role: "DRIVER" as UserRole,
      image:
        "http://localhost:9000/viken-tours/profile-images/1746598119314-italo1.png",
    },
    vehicle: {
      id: "v3",
      model: "Volvo Coach",
      plateNumber: "CD12345",
      type: "BUS" as VehicleType,
      capacity: 16,
      isAvailable: true,
    },
    payment: {
      id: "p5",
      amount: 1500,
      method: "CREDIT_CARD",
      status: "COMPLETED",
    },
  },
  // Novas corridas com minivan e ônibus para a conta de exemplo
  {
    id: "17",
    customerId: "customer1",
    driverId: "driver1",
    vehicleId: "v5",
    status: "COMPLETED" as BookingStatus,
    pickupLocation: "Tøyen, Oslo",
    dropoffLocation: "Sandvika",
    scheduledTime: createRelativeDate(-5, 13, 0), // 5 dias atrás às 13:00
    passengers: 6,
    specialRequests: "Family trip with luggage",
    createdAt: new Date(new Date().getTime() - 7 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(new Date().getTime() - 5 * 24 * 60 * 60 * 1000),
    customer: {
      id: "customer1",
      email: "<EMAIL>",
      name: "John Doe",
      phone: "+47 123 45 678",
      role: "CUSTOMER" as UserRole,
    },
    driver: {
      id: "driver1",
      email: "<EMAIL>",
      name: "Test 1 Hansen",
      phone: "+47 99452211",
      role: "DRIVER" as UserRole,
      image:
        "http://localhost:9000/viken-tours/profile-images/1746598119314-italo1.png",
    },
    vehicle: {
      id: "v5",
      model: "Toyota Sienna",
      plateNumber: "GH34567",
      type: "MINIVAN" as VehicleType,
      capacity: 7,
      isAvailable: true,
    },
    payment: {
      id: "p7",
      amount: 1100,
      method: "CREDIT_CARD",
      status: "COMPLETED",
    },
  },
  {
    id: "18",
    customerId: "customer1",
    driverId: "driver1",
    vehicleId: "v5",
    status: "COMPLETED" as BookingStatus,
    pickupLocation: "Nydalen, Oslo",
    dropoffLocation: "Lillestrøm",
    scheduledTime: createRelativeDate(-10, 9, 15), // 10 dias atrás às 9:15
    passengers: 5,
    specialRequests: "Airport transfer with luggage",
    createdAt: new Date(new Date().getTime() - 15 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(new Date().getTime() - 10 * 24 * 60 * 60 * 1000),
    customer: {
      id: "customer1",
      email: "<EMAIL>",
      name: "John Doe",
      phone: "+47 123 45 678",
      role: "CUSTOMER" as UserRole,
    },
    driver: {
      id: "driver1",
      email: "<EMAIL>",
      name: "Test 1 Hansen",
      phone: "+47 99452211",
      role: "DRIVER" as UserRole,
      image:
        "http://localhost:9000/viken-tours/profile-images/1746598119314-italo1.png",
    },
    vehicle: {
      id: "v5",
      model: "Toyota Sienna",
      plateNumber: "GH34567",
      type: "MINIVAN" as VehicleType,
      capacity: 7,
      isAvailable: true,
    },
    payment: {
      id: "p8",
      amount: 950,
      method: "CREDIT_CARD",
      status: "COMPLETED",
    },
  },
  {
    id: "19",
    customerId: "customer1",
    driverId: "driver1",
    vehicleId: "v3",
    status: "COMPLETED" as BookingStatus,
    pickupLocation: "Oslo Sentrum",
    dropoffLocation: "Drammen",
    scheduledTime: createRelativeDate(-15, 10, 0), // 15 dias atrás às 10:00
    passengers: 14,
    specialRequests: "Group tour",
    createdAt: new Date(new Date().getTime() - 20 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(new Date().getTime() - 15 * 24 * 60 * 60 * 1000),
    customer: {
      id: "customer1",
      email: "<EMAIL>",
      name: "John Doe",
      phone: "+47 123 45 678",
      role: "CUSTOMER" as UserRole,
    },
    driver: {
      id: "driver1",
      email: "<EMAIL>",
      name: "Test 1 Hansen",
      phone: "+47 99452211",
      role: "DRIVER" as UserRole,
      image:
        "http://localhost:9000/viken-tours/profile-images/1746598119314-italo1.png",
    },
    vehicle: {
      id: "v3",
      model: "Volvo Coach",
      plateNumber: "CD12345",
      type: "BUS" as VehicleType,
      capacity: 16,
      isAvailable: true,
    },
    payment: {
      id: "p9",
      amount: 1800,
      method: "CREDIT_CARD",
      status: "COMPLETED",
    },
  },
  // Novas corridas adicionadas para garantir que haja dados suficientes
  // Corridas COMPLETED recentes
  {
    id: "20",
    customerId: "customer1",
    driverId: "driver1",
    vehicleId: "v1",
    status: "COMPLETED" as BookingStatus,
    pickupLocation: "Nationaltheatret, Oslo",
    dropoffLocation: "Frognerparken",
    scheduledTime: createRandomTimeRelativeDate(-1), // Ontem
    passengers: 2,
    specialRequests: null,
    createdAt: new Date(new Date().getTime() - 2 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(new Date().getTime() - 1 * 24 * 60 * 60 * 1000),
    customer: {
      id: "customer1",
      email: "<EMAIL>",
      name: "John Doe",
      phone: "+47 123 45 678",
      role: "CUSTOMER" as UserRole,
    },
    driver: {
      id: "driver1",
      email: "<EMAIL>",
      name: "Test 1 Hansen",
      phone: "+47 99452211",
      role: "DRIVER" as UserRole,
      image:
        "http://localhost:9000/viken-tours/profile-images/1746598119314-italo1.png",
    },
    vehicle: {
      id: "v1",
      model: "Tesla Model Y",
      plateNumber: "EL12345",
      type: "VIP_SEDAN" as VehicleType,
      capacity: 4,
      isAvailable: true,
    },
    payment: {
      id: "p20",
      amount: 350,
      method: "CREDIT_CARD",
      status: "COMPLETED",
    },
  },
  {
    id: "21",
    customerId: "customer1",
    driverId: "driver1",
    vehicleId: "v2",
    status: "COMPLETED" as BookingStatus,
    pickupLocation: "Aker Brygge",
    dropoffLocation: "Oslo S",
    scheduledTime: createRandomTimeRelativeDate(-2), // 2 dias atrás
    passengers: 6,
    specialRequests: "Grupo de turistas",
    createdAt: new Date(new Date().getTime() - 3 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(new Date().getTime() - 2 * 24 * 60 * 60 * 1000),
    customer: {
      id: "customer1",
      email: "<EMAIL>",
      name: "John Doe",
      phone: "+47 123 45 678",
      role: "CUSTOMER" as UserRole,
    },
    driver: {
      id: "driver1",
      email: "<EMAIL>",
      name: "Test 1 Hansen",
      phone: "+47 99452211",
      role: "DRIVER" as UserRole,
      image:
        "http://localhost:9000/viken-tours/profile-images/1746598119314-italo1.png",
    },
    vehicle: {
      id: "v2",
      model: "Mercedes Sprinter",
      plateNumber: "AB67890",
      type: "MINIBUS" as VehicleType,
      capacity: 8,
      isAvailable: true,
    },
    payment: {
      id: "p21",
      amount: 750,
      method: "CREDIT_CARD",
      status: "COMPLETED",
    },
  },
  // Corridas CANCELLED recentes
  {
    id: "22",
    customerId: "customer1",
    driverId: "driver1",
    vehicleId: "v1",
    status: "CANCELLED" as BookingStatus,
    pickupLocation: "Majorstuen",
    dropoffLocation: "Bislett",
    scheduledTime: createRandomTimeRelativeDate(-3), // 3 dias atrás
    passengers: 1,
    specialRequests: null,
    createdAt: new Date(new Date().getTime() - 5 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(new Date().getTime() - 3 * 24 * 60 * 60 * 1000),
    customer: {
      id: "customer1",
      email: "<EMAIL>",
      name: "John Doe",
      phone: "+47 123 45 678",
      role: "CUSTOMER" as UserRole,
    },
    driver: {
      id: "driver1",
      email: "<EMAIL>",
      name: "Test 1 Hansen",
      phone: "+47 99452211",
      role: "DRIVER" as UserRole,
      image:
        "http://localhost:9000/viken-tours/profile-images/1746598119314-italo1.png",
    },
    vehicle: {
      id: "v1",
      model: "Tesla Model Y",
      plateNumber: "EL12345",
      type: "VIP_SEDAN" as VehicleType,
      capacity: 4,
      isAvailable: true,
    },
    payment: {
      id: "p22",
      amount: 200,
      method: "CREDIT_CARD",
      status: "REFUNDED",
    },
  },
  // Corridas com diferentes tipos de veículos
  {
    id: "23",
    customerId: "customer1",
    driverId: "driver1",
    vehicleId: "v3",
    status: "COMPLETED" as BookingStatus,
    pickupLocation: "Oslo Sentrum",
    dropoffLocation: "Holmenkollen",
    scheduledTime: createRandomTimeRelativeDate(-4), // 4 dias atrás
    passengers: 15,
    specialRequests: "Excursão escolar",
    createdAt: new Date(new Date().getTime() - 6 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(new Date().getTime() - 4 * 24 * 60 * 60 * 1000),
    customer: {
      id: "customer1",
      email: "<EMAIL>",
      name: "John Doe",
      phone: "+47 123 45 678",
      role: "CUSTOMER" as UserRole,
    },
    driver: {
      id: "driver1",
      email: "<EMAIL>",
      name: "Test 1 Hansen",
      phone: "+47 99452211",
      role: "DRIVER" as UserRole,
      image:
        "http://localhost:9000/viken-tours/profile-images/1746598119314-italo1.png",
    },
    vehicle: {
      id: "v3",
      model: "Volvo Coach",
      plateNumber: "CD12345",
      type: "BUS" as VehicleType,
      capacity: 16,
      isAvailable: true,
    },
    payment: {
      id: "p23",
      amount: 1600,
      method: "CREDIT_CARD",
      status: "COMPLETED",
    },
  },
  {
    id: "24",
    customerId: "customer1",
    driverId: "driver1",
    vehicleId: "v5",
    status: "COMPLETED" as BookingStatus,
    pickupLocation: "Grünerløkka",
    dropoffLocation: "Bygdøy",
    scheduledTime: createRandomTimeRelativeDate(-5), // 5 dias atrás
    passengers: 5,
    specialRequests: "Família com crianças",
    createdAt: new Date(new Date().getTime() - 7 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(new Date().getTime() - 5 * 24 * 60 * 60 * 1000),
    customer: {
      id: "customer1",
      email: "<EMAIL>",
      name: "John Doe",
      phone: "+47 123 45 678",
      role: "CUSTOMER" as UserRole,
    },
    driver: {
      id: "driver1",
      email: "<EMAIL>",
      name: "Test 1 Hansen",
      phone: "+47 99452211",
      role: "DRIVER" as UserRole,
      image:
        "http://localhost:9000/viken-tours/profile-images/1746598119314-italo1.png",
    },
    vehicle: {
      id: "v5",
      model: "Toyota Sienna",
      plateNumber: "GH34567",
      type: "MINIVAN" as VehicleType,
      capacity: 7,
      isAvailable: true,
    },
    payment: {
      id: "p24",
      amount: 900,
      method: "CREDIT_CARD",
      status: "COMPLETED",
    },
  },
];

// Testfunksjon for å hente alle bestillinger
export async function getBookings() {
  return mockBookings;
}

// Testfunksjon for å hente bestilling etter ID
export async function getBookingById(id: string) {
  return mockBookings.find((booking) => booking.id === id) || null;
}

// Testfunksjon for å hente bestillinger etter status
export async function getBookingsByStatus(status: BookingStatus) {
  return mockBookings.filter((booking) => booking.status === status);
}

// Testfunksjon for å hente bestillinger etter kunde-ID
export async function getBookingsByCustomerId(customerId: string) {
  return mockBookings.filter((booking) => booking.customerId === customerId);
}

// Testfunksjon for å hente bestillinger etter sjåfør-ID
export async function getBookingsByDriverId(driverId: string) {
  return mockBookings.filter((booking) => booking.driverId === driverId);
}

// Funksjon for å oppdatere sjåførinformasjon i alle bestillinger
export function updateDriverInfoInBookings(
  driverId: string,
  driverData: {
    name?: string;
    phone?: string;
    image?: string;
    driverProfile?: any;
  },
  forceUpdateAll: boolean = true
) {
  console.log(`Updating driver info for driver ${driverId}:`, driverData);
  console.log(
    `Driver image: ${driverData.image?.substring(0, 30) || "None"}...`
  );
  console.log(`Force update all bookings: ${forceUpdateAll}`);

  if (driverData.driverProfile) {
    console.log("Driver profile data being updated:", driverData.driverProfile);
  }

  // Garantir que estamos atualizando o driver correto
  // Se o ID não for "driver1", mas for o ID do driver de exemplo, usar "driver1"
  const targetDriverId = driverId === "driver1" ? driverId : "driver1";

  console.log(`Target driver ID for updating bookings: ${targetDriverId}`);

  // Oppdater alle bestillinger som har denne sjåføren
  let updatedCount = 0;

  // Atualizar todas as corridas imediatamente
  // Filtrar as corridas que precisam ser atualizadas
  const bookingsToUpdate = mockBookings.filter(
    (booking) => booking.driverId === targetDriverId && booking.driver
  );

  // Se forceUpdateAll for true, atualizar todas as corridas
  // Caso contrário, limitar a 20 corridas para melhor desempenho
  const bookingsToProcess = forceUpdateAll
    ? bookingsToUpdate
    : bookingsToUpdate.slice(0, 20);

  console.log(`Processing ${bookingsToProcess.length} bookings for update`);

  // Atualizar todas as corridas imediatamente
  bookingsToProcess.forEach((booking) => {
    // Sempre atualizar todas as propriedades para garantir consistência
    if (driverData.name) booking.driver.name = driverData.name;
    if (driverData.phone) booking.driver.phone = driverData.phone;

    // Atualizar a imagem mesmo que seja uma string vazia
    if (driverData.image !== undefined) {
      booking.driver.image = driverData.image;
    }

    // Adicionar campos adicionais do perfil ao driver nas corridas
    if (driverData.driverProfile) {
      // @ts-ignore - Adicionando propriedade driverProfile ao objeto driver
      booking.driver.driverProfile = driverData.driverProfile;
    }

    updatedCount++;
  });

  console.log(`Updated ${updatedCount} bookings with new driver info`);

  // Verificar algumas corridas atualizadas para debug
  if (bookingsToProcess.length > 0) {
    const firstBooking = bookingsToProcess[0];
    console.log(`First updated booking driver: ${firstBooking.driver.name}`);
    console.log(
      `First updated booking driver image: ${firstBooking.driver.image?.substring(
        0,
        30
      )}...`
    );
  }

  // Salvar a última atualização no localStorage para verificação
  if (typeof window !== "undefined") {
    try {
      localStorage.setItem("lastDriverUpdateTimestamp", Date.now().toString());
      localStorage.setItem(
        "lastDriverUpdateInfo",
        JSON.stringify({
          driverId: targetDriverId,
          name: driverData.name,
          phone: driverData.phone,
          updatedCount,
        })
      );
    } catch (e) {
      console.error("Error saving update info to localStorage:", e);
    }
  }

  console.log(
    `Started updating ${bookingsToProcess.length} bookings with new driver info`
  );

  // Atualizar também o usuário mockado
  const driverIndex = mockUsers.findIndex((user) => user.id === targetDriverId);
  if (driverIndex !== -1) {
    if (driverData.name) mockUsers[driverIndex].name = driverData.name;
    if (driverData.phone) mockUsers[driverIndex].phone = driverData.phone;

    // Atualizar a imagem mesmo que seja uma string vazia
    if (driverData.image !== undefined) {
      mockUsers[driverIndex].image = driverData.image;
      console.log(
        `Updated mock user image: ${
          driverData.image?.substring(0, 30) || "None"
        }...`
      );
    }

    // Adicionar campos adicionais do perfil ao usuário mockado
    if (driverData.driverProfile) {
      mockUsers[driverIndex].driverProfile = driverData.driverProfile;
      console.log(
        `Added driver profile data to mock user ${mockUsers[driverIndex].name}`
      );
    }

    console.log(`Updated mock user: ${mockUsers[driverIndex].name}`);

    // Salvar os dados no localStorage para garantir persistência
    if (typeof window !== "undefined") {
      try {
        // Salvar a imagem separadamente para garantir persistência
        if (driverData.image !== undefined) {
          localStorage.setItem("driverProfileImage", driverData.image);
          console.log(
            "Image saved to localStorage from updateDriverInfoInBookings"
          );
        }

        // Salvar cada campo individualmente para garantir persistência
        if (driverData.name)
          localStorage.setItem("driverName", driverData.name);
        if (driverData.phone)
          localStorage.setItem("driverPhone", driverData.phone);

        if (driverData.driverProfile) {
          if (driverData.driverProfile.licenseNumber)
            localStorage.setItem(
              "driverLicenseNumber",
              driverData.driverProfile.licenseNumber
            );
          if (driverData.driverProfile.licenseType)
            localStorage.setItem(
              "driverLicenseType",
              driverData.driverProfile.licenseType
            );
          if (driverData.driverProfile.address)
            localStorage.setItem(
              "driverAddress",
              driverData.driverProfile.address
            );
          if (driverData.driverProfile.city)
            localStorage.setItem("driverCity", driverData.driverProfile.city);
          if (driverData.driverProfile.bio)
            localStorage.setItem("driverBio", driverData.driverProfile.bio);

          // Salvar o perfil completo
          localStorage.setItem(
            "driverProfile",
            JSON.stringify({
              id: mockUsers[driverIndex].id,
              name: mockUsers[driverIndex].name,
              phone: mockUsers[driverIndex].phone,
              image: mockUsers[driverIndex].image,
              email: mockUsers[driverIndex].email,
              driverProfile: driverData.driverProfile,
            })
          );
          console.log(
            "Driver profile saved to localStorage from updateDriverInfoInBookings"
          );
        }
      } catch (e) {
        console.error("Error saving to localStorage:", e);
      }
    }
  }

  // Verificar se a atualização foi bem-sucedida
  const verifyBooking = mockBookings.find((b) => b.driverId === targetDriverId);
  if (verifyBooking && verifyBooking.driver) {
    console.log(`Verification - Driver name: ${verifyBooking.driver.name}`);
    console.log(`Verification - Driver phone: ${verifyBooking.driver.phone}`);
    console.log(
      `Verification - Driver image: ${
        verifyBooking.driver.image?.substring(0, 30) || "None"
      }...`
    );

    // @ts-ignore - Verificando propriedade driverProfile
    if (verifyBooking.driver.driverProfile) {
      console.log(`Verification - Driver profile data exists in booking`);
      // @ts-ignore - Acessando propriedades do driverProfile
      console.log(
        `Verification - Driver license: ${
          verifyBooking.driver.driverProfile.licenseNumber || "Not set"
        }`
      );
    }
  }
}

// Testfunksjon for å hente dagens bestillinger
export async function getTodaysBookings() {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  return mockBookings.filter((booking) => {
    const bookingDate = new Date(booking.scheduledTime);
    return bookingDate >= today && bookingDate < tomorrow;
  });
}

// Testfunksjon for å hente antall ventende bestillinger
export async function getPendingBookingsCount() {
  return mockBookings.filter((booking) => booking.status === "PENDING").length;
}

// Testdata for brukere - oppdatert for å matche innloggingsinstruksjoner
export const mockUsers = [
  {
    id: "admin1",
    email: "<EMAIL>",
    name: "Administrator",
    phone: "+47 123 45 678",
    passwordHash: "$2a$10$XXXXXXXXXXXXXXXXXXXXXXXX", // Ikke en ekte hash
    role: "ADMIN",
    createdAt: new Date(2023, 0, 1),
    updatedAt: new Date(2023, 0, 1),
  },
  {
    id: "customer1",
    email: "<EMAIL>",
    name: "John Doe",
    phone: "+47 234 56 789",
    passwordHash: "$2a$10$XXXXXXXXXXXXXXXXXXXXXXXX", // Ikke en ekte hash
    role: "CUSTOMER",
    createdAt: new Date(2023, 1, 15),
    updatedAt: new Date(2023, 1, 15),
  },
  {
    id: "c2",
    email: "<EMAIL>",
    name: "Maria Olsen",
    phone: "+47 345 67 890",
    passwordHash: "$2a$10$XXXXXXXXXXXXXXXXXXXXXXXX", // Ikke en ekte hash
    role: "CUSTOMER",
    createdAt: new Date(2023, 2, 10),
    updatedAt: new Date(2023, 2, 10),
  },
  {
    id: "c3",
    email: "<EMAIL>",
    name: "Company AS",
    phone: "+47 456 78 901",
    passwordHash: "$2a$10$XXXXXXXXXXXXXXXXXXXXXXXX", // Ikke en ekte hash
    role: "CUSTOMER",
    createdAt: new Date(2023, 3, 5),
    updatedAt: new Date(2023, 3, 5),
  },
  {
    id: "driver1",
    email: "<EMAIL>",
    name: "Test 1 Hansen",
    phone: "+47 99452211",
    passwordHash: "$2a$10$XXXXXXXXXXXXXXXXXXXXXXXX", // Ikke en ekte hash
    role: "DRIVER",
    createdAt: new Date(2023, 1, 1),
    updatedAt: new Date(2023, 1, 1),
    image:
      "http://localhost:9000/viken-tours/profile-images/1746598119314-italo1.png",
  },
  {
    id: "d2",
    email: "<EMAIL>",
    name: "Ole Nordmann",
    phone: "+47 678 90 123",
    passwordHash: "$2a$10$XXXXXXXXXXXXXXXXXXXXXXXX", // Ikke en ekte hash
    role: "DRIVER",
    createdAt: new Date(2023, 2, 1),
    updatedAt: new Date(2023, 2, 1),
  },
];

// Testfunksjon for å hente alle brukere
export async function getUsers() {
  return mockUsers;
}

// Testfunksjon for å hente bruker etter ID
export async function getUserById(id: string) {
  return mockUsers.find((user) => user.id === id) || null;
}

// Testfunksjon for å hente brukere etter rolle
export async function getUsersByRole(role: string) {
  return mockUsers.filter((user) => user.role === role);
}

// Testdata for kjøretøy
export const mockVehicles = [
  {
    id: "v1",
    type: "VIP_SEDAN",
    model: "Tesla Model Y",
    plateNumber: "EL12345",
    capacity: 4,
    isAvailable: true,
    createdAt: new Date(2023, 0, 15),
    updatedAt: new Date(2023, 0, 15),
  },
  {
    id: "v2",
    type: "MINIBUS",
    model: "Mercedes Sprinter",
    plateNumber: "AB67890",
    capacity: 8,
    isAvailable: true,
    createdAt: new Date(2023, 1, 10),
    updatedAt: new Date(2023, 1, 10),
  },
  {
    id: "v3",
    type: "BUS",
    model: "Volvo Coach",
    plateNumber: "CD12345",
    capacity: 16,
    isAvailable: true,
    createdAt: new Date(2023, 2, 5),
    updatedAt: new Date(2023, 2, 5),
  },
  {
    id: "v4",
    type: "VIP_SEDAN",
    model: "BMW 7 Series",
    plateNumber: "EF23456",
    capacity: 4,
    isAvailable: false,
    createdAt: new Date(2023, 3, 20),
    updatedAt: new Date(2023, 3, 20),
  },
  {
    id: "v5",
    type: "MINIVAN",
    model: "Toyota Sienna",
    plateNumber: "GH34567",
    capacity: 7,
    isAvailable: true,
    createdAt: new Date(2023, 4, 15),
    updatedAt: new Date(2023, 4, 15),
  },
];

// Testfunksjon for å hente alle kjøretøy
export async function getVehicles() {
  return mockVehicles;
}

// Testfunksjon for å hente kjøretøy etter ID
export async function getVehicleById(id: string) {
  return mockVehicles.find((vehicle) => vehicle.id === id) || null;
}

// Testfunksjon for å hente tilgjengelige kjøretøy
export async function getAvailableVehicles() {
  return mockVehicles.filter((vehicle) => vehicle.isAvailable);
}

// Teststatistikk for dashbord
export const mockStats = {
  totalBookings: mockBookings.length,
  pendingBookings: mockBookings.filter(
    (booking) => booking.status === "PENDING"
  ).length,
  completedBookings: mockBookings.filter(
    (booking) => booking.status === "COMPLETED"
  ).length,
  cancelledBookings: mockBookings.filter(
    (booking) => booking.status === "CANCELLED"
  ).length,
  activeDrivers: 2,
  availableVehicles: mockVehicles.filter((vehicle) => vehicle.isAvailable)
    .length,
  totalRevenue: 2950,
  averageRating: 4.7,
};
