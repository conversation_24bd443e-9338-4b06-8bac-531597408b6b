// Função para carregar dados mockados do localStorage
import { mockUsers, mockBookings } from "@/lib/mock-data";

// Função para carregar os dados do perfil do driver do localStorage
export function loadMockDataFromStorage() {
  if (typeof window === "undefined") {
    return; // Não executar no servidor
  }

  try {
    console.log("Loading mock data from localStorage...");

    // Carregar os dados do perfil do driver do localStorage
    const driverProfileStr = localStorage.getItem("driverProfile");
    const driverProfileImage = localStorage.getItem("driverProfileImage");

    // Carregar campos individuais para garantir que temos todos os dados
    const driverName = localStorage.getItem("driverName");
    const driverPhone = localStorage.getItem("driverPhone");
    const driverLicenseNumber = localStorage.getItem("driverLicenseNumber");
    const driverLicenseType = localStorage.getItem("driverLicenseType");
    const driverAddress = localStorage.getItem("driverAddress");
    const driverCity = localStorage.getItem("driverCity");
    const driverBio = localStorage.getItem("driverBio");
    const driverPostalCode = localStorage.getItem("driverPostalCode");
    const driverExperienceYears = localStorage.getItem("driverExperienceYears");
    const driverPreferredVehicleType = localStorage.getItem(
      "driverPreferredVehicleType"
    );
    const driverAvailableWeekends = localStorage.getItem(
      "driverAvailableWeekends"
    );
    const driverAvailableNights = localStorage.getItem("driverAvailableNights");
    const driverEmergencyContactName = localStorage.getItem(
      "driverEmergencyContactName"
    );
    const driverEmergencyContactPhone = localStorage.getItem(
      "driverEmergencyContactPhone"
    );

    // Verificar se temos dados do perfil
    if (driverProfileStr || driverName || driverProfileImage) {
      let driverProfile = driverProfileStr
        ? JSON.parse(driverProfileStr)
        : null;

      console.log(
        "Found driver profile in localStorage:",
        driverName || driverProfile?.name || "Unknown"
      );
      console.log(
        "Profile image in localStorage:",
        driverProfileImage
          ? driverProfileImage.substring(0, 30) + "..."
          : "None"
      );

      // Atualizar o driver nos dados mockados
      const driverIndex = mockUsers.findIndex(
        (user) => user.id === "driver1" || user.email === "<EMAIL>"
      );

      if (driverIndex !== -1) {
        // Atualizar todos os campos do perfil
        if (driverName || (driverProfile && driverProfile.name)) {
          mockUsers[driverIndex].name = driverName || driverProfile.name;
        }

        if (driverPhone || (driverProfile && driverProfile.phone)) {
          mockUsers[driverIndex].phone = driverPhone || driverProfile.phone;
        }

        // Usar a imagem do item separado se disponível, caso contrário usar a do perfil
        const imageToUse =
          driverProfileImage || (driverProfile ? driverProfile.image : null);
        if (imageToUse) {
          mockUsers[driverIndex].image = imageToUse;
          console.log(
            "Updated driver image from localStorage:",
            imageToUse.substring(0, 30) + "..."
          );
        }

        // Adicionar campos adicionais do perfil como propriedades do usuário
        mockUsers[driverIndex].driverProfile = {
          licenseNumber:
            driverLicenseNumber ||
            driverProfile?.driverProfile?.licenseNumber ||
            "",
          licenseType:
            driverLicenseType ||
            driverProfile?.driverProfile?.licenseType ||
            "",
          experienceYears: driverExperienceYears
            ? parseInt(driverExperienceYears)
            : driverProfile?.driverProfile?.experienceYears || 0,
          preferredVehicleType:
            driverPreferredVehicleType ||
            driverProfile?.driverProfile?.preferredVehicleType ||
            "",
          availableWeekends:
            driverAvailableWeekends === "true"
              ? true
              : driverAvailableWeekends === "false"
              ? false
              : driverProfile?.driverProfile?.availableWeekends || false,
          availableNights:
            driverAvailableNights === "true"
              ? true
              : driverAvailableNights === "false"
              ? false
              : driverProfile?.driverProfile?.availableNights || false,
          emergencyContactName:
            driverEmergencyContactName ||
            driverProfile?.driverProfile?.emergencyContactName ||
            "",
          emergencyContactPhone:
            driverEmergencyContactPhone ||
            driverProfile?.driverProfile?.emergencyContactPhone ||
            "",
          bio: driverBio || driverProfile?.driverProfile?.bio || "",
          address: driverAddress || driverProfile?.driverProfile?.address || "",
          postalCode:
            driverPostalCode || driverProfile?.driverProfile?.postalCode || "",
          city: driverCity || driverProfile?.driverProfile?.city || "",
        };

        console.log(
          "Updated mock user from localStorage:",
          mockUsers[driverIndex].name
        );
        console.log(
          "Updated mock user image:",
          mockUsers[driverIndex].image
            ? mockUsers[driverIndex].image.substring(0, 30) + "..."
            : "None"
        );
        console.log(
          "Updated driver profile data:",
          mockUsers[driverIndex].driverProfile
        );

        // Atualizar o driver em todas as corridas
        let updatedCount = 0;
        mockBookings.forEach((booking) => {
          if (booking.driverId === "driver1" && booking.driver) {
            // Atualizar dados básicos
            booking.driver.name = mockUsers[driverIndex].name;
            booking.driver.phone = mockUsers[driverIndex].phone || "";

            // Usar a imagem do item separado se disponível
            if (imageToUse) {
              booking.driver.image = imageToUse;
              console.log(
                `Updated image for booking ${
                  booking.id
                }: ${imageToUse.substring(0, 30)}...`
              );
            }

            // Adicionar campos adicionais do perfil ao driver nas corridas
            // @ts-ignore - Adicionando propriedade driverProfile ao objeto driver
            booking.driver.driverProfile = mockUsers[driverIndex].driverProfile;

            updatedCount++;
          }
        });

        console.log(
          `Updated driver info in ${updatedCount} bookings from localStorage`
        );

        // Verificar se a atualização foi bem-sucedida
        const verifyBooking = mockBookings.find(
          (b) => b.driverId === "driver1"
        );
        if (verifyBooking && verifyBooking.driver) {
          console.log(
            `Verification - Booking driver name: ${verifyBooking.driver.name}`
          );
          console.log(
            `Verification - Booking driver image: ${
              verifyBooking.driver.image
                ? verifyBooking.driver.image.substring(0, 30) + "..."
                : "None"
            }`
          );
          // @ts-ignore - Verificando propriedade driverProfile
          if (verifyBooking.driver.driverProfile) {
            console.log(`Verification - Booking driver profile data exists`);
            // @ts-ignore - Acessando propriedades do driverProfile
            console.log(
              `Verification - Driver license: ${
                verifyBooking.driver.driverProfile.licenseNumber || "Not set"
              }`
            );
            // @ts-ignore - Acessando propriedades do driverProfile
            console.log(
              `Verification - Driver address: ${
                verifyBooking.driver.driverProfile.address || "Not set"
              }`
            );
          }
        }

        // Verificar todas as corridas para garantir que a imagem foi atualizada
        const bookingsWithDriver = mockBookings.filter(
          (b) => b.driverId === "driver1" && b.driver
        );
        console.log(`Found ${bookingsWithDriver.length} bookings with driver1`);

        if (bookingsWithDriver.length > 0) {
          const firstBooking = bookingsWithDriver[0];
          console.log(`First booking driver name: ${firstBooking.driver.name}`);
          console.log(
            `First booking driver image: ${
              firstBooking.driver.image
                ? firstBooking.driver.image.substring(0, 30) + "..."
                : "None"
            }`
          );

          const lastBooking = bookingsWithDriver[bookingsWithDriver.length - 1];
          console.log(`Last booking driver name: ${lastBooking.driver.name}`);
          console.log(
            `Last booking driver image: ${
              lastBooking.driver.image
                ? lastBooking.driver.image.substring(0, 30) + "..."
                : "None"
            }`
          );
        }
      }
    }
  } catch (error) {
    console.error("Error loading mock data from localStorage:", error);
  }
}

// Função para limpar o cache do navegador
export function clearBrowserCache() {
  if (typeof window === "undefined") {
    return; // Não executar no servidor
  }

  try {
    // Forçar um recarregamento da página sem usar o cache
    window.location.reload(true);
  } catch (error) {
    console.error("Error clearing browser cache:", error);
  }
}
